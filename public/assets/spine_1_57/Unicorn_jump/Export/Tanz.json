{"skeleton": {"hash": "+g9I+bpMfZA", "spine": "4.2.40", "x": -169, "y": -53.02, "width": 336.58, "height": 167, "images": "./Images/Monster_Tanz/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "main", "parent": "root"}, {"name": "body_cntrl", "parent": "main", "x": -0.87, "y": 8, "color": "373c8eff", "icon": "arrows"}, {"name": "body", "parent": "body_cntrl", "y": 53.57, "color": "373c8eff", "icon": "diamond"}, {"name": "body_cntrl_left", "parent": "body", "length": 88.25, "rotation": -177.52, "x": -32.79, "y": -0.95, "color": "ff0101ff"}, {"name": "body_cntrl_rigth", "parent": "body", "length": 97.34, "rotation": -2.72, "x": 39.7, "y": -0.98, "color": "ff0101ff"}, {"name": "IK_Main", "parent": "main", "icon": "circle"}, {"name": "ik_left", "parent": "IK_Main", "rotation": -177.52, "x": -121.83, "y": 56.8, "color": "ff0101ff", "icon": "ik"}, {"name": "ik_rigth", "parent": "IK_Main", "rotation": -2.72, "x": 136.05, "y": 55.96, "color": "fb7d00ff", "icon": "ik"}, {"name": "body3", "parent": "body", "rotation": -90, "x": -22.28, "y": -45.37, "color": "373c8eff", "icon": "diamond"}, {"name": "big_leg", "parent": "body3", "length": 27.5, "color": "373c8eff"}, {"name": "big_leg2", "parent": "big_leg", "length": 16.5, "x": 27.5, "color": "373c8eff"}, {"name": "big_leg3", "parent": "big_leg2", "length": 11, "x": 16.5, "color": "373c8eff"}, {"name": "big_leg4", "parent": "big_leg3", "length": 5.5, "x": 11, "color": "373c8eff"}, {"name": "body4", "parent": "body", "rotation": -88.67, "x": -62.22, "y": -31.93, "color": "373c8eff", "icon": "diamond"}, {"name": "body2", "parent": "body4", "length": 21.35, "color": "373c8eff"}, {"name": "body2b", "parent": "body2", "length": 12.81, "x": 21.35, "color": "373c8eff"}, {"name": "body2c", "parent": "body2b", "length": 8.54, "x": 12.81, "color": "373c8eff"}, {"name": "body2d", "parent": "body2c", "length": 4.27, "x": 8.54, "color": "373c8eff"}, {"name": "body5", "parent": "body", "rotation": -90, "x": 30.88, "y": -45.37, "scaleY": -1, "color": "373c8eff", "icon": "diamond"}, {"name": "big_leg5", "parent": "body5", "length": 27.5, "color": "373c8eff"}, {"name": "big_leg6", "parent": "big_leg5", "length": 16.5, "x": 27.5, "color": "373c8eff"}, {"name": "big_leg7", "parent": "big_leg6", "length": 11, "x": 16.5, "color": "373c8eff"}, {"name": "big_leg8", "parent": "big_leg7", "length": 5.5, "x": 11, "color": "373c8eff"}, {"name": "body8", "parent": "body", "rotation": -88.67, "x": 65.2, "y": -32.27, "scaleY": -1, "color": "373c8eff", "icon": "diamond"}, {"name": "body9", "parent": "body8", "length": 21.35, "color": "373c8eff"}, {"name": "body2b3", "parent": "body9", "length": 12.81, "x": 21.35, "color": "373c8eff"}, {"name": "body2c3", "parent": "body2b3", "length": 8.54, "x": 12.81, "color": "373c8eff"}, {"name": "body2d3", "parent": "body2c3", "length": 4.27, "x": 8.54, "color": "373c8eff"}, {"name": "eye_L", "parent": "ik_left", "x": 5.26, "y": 32.52, "color": "ff0101ff", "icon": "eye"}, {"name": "eye_L2", "parent": "eye_L", "x": -7.01, "y": -28.57, "color": "ff0101ff"}, {"name": "eye_L3", "parent": "eye_L", "rotation": 20.36, "x": -4.44, "y": -19.13, "color": "ff0101ff", "icon": "eye"}, {"name": "ik_rigth2", "parent": "ik_rigth", "x": -6.59, "y": -32.45, "color": "fb7d00ff", "icon": "eye"}, {"name": "ik_rigth3", "parent": "ik_rigth2", "x": 1.41, "y": 19.58, "color": "c88746ff", "icon": "eye"}, {"name": "ik_rigth4", "parent": "ik_rigth2", "x": 3.54, "y": 29.5, "color": "fb7d00ff"}, {"name": "eye_L4", "parent": "eye_L", "rotation": 21.32, "x": -3.41, "y": -21.27, "color": "ff0101ff", "icon": "eye"}, {"name": "ik_rigth5", "parent": "ik_rigth2", "rotation": 5.04, "x": -1.75, "y": 22.4, "color": "38fb00ff", "icon": "eye"}, {"name": "ik_rigth6", "parent": "ik_rigth2", "rotation": 8.35, "x": -8.54, "y": 80.32, "color": "fb7d00ff", "icon": "eye"}, {"name": "eye_L5", "parent": "eye_L", "rotation": -29.29, "x": -9.8, "y": -76.6, "color": "ff0101ff", "icon": "eye"}, {"name": "ma<PERSON>", "parent": "body", "x": 1.14, "y": -21.06, "color": "373c8eff", "icon": "mouth"}, {"name": "mauth2", "parent": "ma<PERSON>", "x": 0.34, "y": 7.86, "color": "373c8eff", "icon": "mouth"}, {"name": "mauth3", "parent": "ma<PERSON>", "x": -65.23, "y": 6.49, "color": "373c8eff", "icon": "mouth"}, {"name": "mauth4", "parent": "ma<PERSON>", "x": 61.82, "y": 6.83, "color": "373c8eff", "icon": "mouth"}, {"name": "death", "parent": "root"}, {"name": "blot", "parent": "death", "rotation": -0.04, "icon": "flower"}, {"name": "blot_drops_control", "parent": "blot", "y": -10.68, "icon": "arrowDown"}, {"name": "Drops", "parent": "blot", "rotation": 0.04}, {"name": "blot_drop2", "parent": "Drops"}, {"name": "blot_drop_s1", "parent": "Drops"}, {"name": "blot_drop3", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop4", "parent": "Drops", "scaleX": 1.2553, "scaleY": 1.2553}, {"name": "blot_drop_s2", "parent": "Drops"}, {"name": "blot_drop5", "parent": "Drops"}, {"name": "blot_drop_s3", "parent": "Drops"}, {"name": "blot_drop6", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop_s4", "parent": "Drops"}], "slots": [{"name": "body_outline", "bone": "main", "attachment": "body_outline"}, {"name": "leg_big_outline", "bone": "main", "attachment": "leg_big_outline"}, {"name": "leg_big_outline2", "bone": "body5", "attachment": "leg_big_outline"}, {"name": "leg_small_outline", "bone": "main", "attachment": "leg_small_outline"}, {"name": "leg_small_outline2", "bone": "body8", "attachment": "leg_small_outline"}, {"name": "body", "bone": "main", "attachment": "body"}, {"name": "leg_big", "bone": "big_leg", "attachment": "leg_big"}, {"name": "leg_big3", "bone": "big_leg5", "attachment": "leg_big"}, {"name": "leg_big2", "bone": "main", "attachment": "leg_big"}, {"name": "leg_small", "bone": "body2", "attachment": "leg_small"}, {"name": "leg_small3", "bone": "body9", "attachment": "leg_small"}, {"name": "mouth", "bone": "ma<PERSON>", "attachment": "mouth"}, {"name": "teeth", "bone": "ma<PERSON>", "attachment": "teeth"}, {"name": "eye_r", "bone": "main", "attachment": "eye_r"}, {"name": "pupil_r", "bone": "ik_rigth4", "attachment": "pupil_r"}, {"name": "eyelid_u_r", "bone": "main", "attachment": "eyelid_u_r"}, {"name": "eyelid_l_r", "bone": "main", "attachment": "eyelid_l_r"}, {"name": "brow_r", "bone": "ik_rigth6", "attachment": "brow_r"}, {"name": "eye_l", "bone": "main", "attachment": "eye_l"}, {"name": "pupil_l", "bone": "eye_L2", "attachment": "pupil_l"}, {"name": "eyelid_u_l", "bone": "main", "attachment": "eyelid_u_l"}, {"name": "eyelid_l_l", "bone": "main", "attachment": "eyelid_l_l"}, {"name": "brow_l", "bone": "eye_L5", "attachment": "brow_l"}, {"name": "blot", "bone": "blot", "color": "06aefaff", "dark": "ffffff"}, {"name": "blot_drop2", "bone": "blot_drop2", "color": "06aefaff", "dark": "ffffff"}, {"name": "blot_drop_s1", "bone": "blot_drop_s1", "color": "06aefaff", "dark": "ffffff"}, {"name": "blot_drop3", "bone": "blot_drop3", "color": "06aefaff", "dark": "ffffff"}, {"name": "blot_drop4", "bone": "blot_drop4", "color": "06aefaff", "dark": "ffffff"}, {"name": "blot_drop5", "bone": "blot_drop_s2", "color": "06aefaff", "dark": "ffffff"}, {"name": "blot_drop6", "bone": "blot_drop5", "color": "06aefaff", "dark": "ffffff"}, {"name": "blot_drop_s2", "bone": "blot_drop_s3", "color": "06aefaff", "dark": "ffffff"}, {"name": "blot_drop7", "bone": "blot_drop6", "color": "06aefaff", "dark": "ffffff"}, {"name": "blot_drop8", "bone": "blot_drop_s4", "color": "06aefaff", "dark": "ffffff"}], "ik": [{"name": "ik_left", "bones": ["body_cntrl_left"], "target": "ik_left", "compress": true, "stretch": true}, {"name": "ik_rigth", "order": 1, "bones": ["body_cntrl_rigth"], "target": "ik_rigth", "compress": true, "stretch": true}], "transform": [{"name": "tr_eye_L", "order": 2, "bones": ["ik_rigth6"], "target": "ik_rigth5", "rotation": 3.31, "x": -1.68, "y": 58.29, "mixRotate": 0.45, "mixX": 0.45, "mixScaleX": 0.45, "mixShearY": 0.45}, {"name": "tr_eye_r", "order": 3, "bones": ["eye_L5"], "target": "eye_L4", "rotation": -50.62, "x": -26.07, "y": -49.22, "mixRotate": 0.4, "mixX": 0.4, "mixScaleX": 0.4, "mixShearY": 0.4}], "skins": [{"name": "default", "attachments": {"blot": {"blot": {"type": "mesh", "uvs": [0.16853, 0.04132, 0.20427, 0.04133, 0.235, 0.05776, 0.2567, 0.08503, 0.27019, 0.11893, 0.28083, 0.15284, 0.29008, 0.16859, 0.40306, 0.14175, 0.52511, 0.14243, 0.63958, 0.17341, 0.65099, 0.15069, 0.66785, 0.11583, 0.69938, 0.10078, 0.74656, 0.0982, 0.78663, 0.11447, 0.81478, 0.14412, 0.82679, 0.17844, 0.8274, 0.22268, 0.80551, 0.24979, 0.78146, 0.26764, 0.77045, 0.28539, 0.79007, 0.31899, 0.82203, 0.31695, 0.86186, 0.3347, 0.88752, 0.35974, 0.90407, 0.39632, 0.9038, 0.43154, 0.89703, 0.47361, 0.87282, 0.49811, 0.84367, 0.51769, 0.83976, 0.57695, 0.82721, 0.62819, 0.85091, 0.63399, 0.88661, 0.64029, 0.9085, 0.64943, 0.92589, 0.66799, 0.92962, 0.68768, 0.92352, 0.71185, 0.90345, 0.72913, 0.88078, 0.7345, 0.85869, 0.73204, 0.84233, 0.71683, 0.82827, 0.69353, 0.80387, 0.68283, 0.78923, 0.70054, 0.77272, 0.72456, 0.75456, 0.74543, 0.74252, 0.76337, 0.75174, 0.78241, 0.7707, 0.79541, 0.78966, 0.80098, 0.80915, 0.81654, 0.82604, 0.83018, 0.83576, 0.85319, 0.83587, 0.87721, 0.83318, 0.90725, 0.81778, 0.92725, 0.79106, 0.94395, 0.74837, 0.94704, 0.71572, 0.94051, 0.69219, 0.91664, 0.67776, 0.88946, 0.6729, 0.86403, 0.66254, 0.84197, 0.64012, 0.83666, 0.62051, 0.84647, 0.61585, 0.88202, 0.60876, 0.90761, 0.58596, 0.93801, 0.55205, 0.95614, 0.51472, 0.9587, 0.47446, 0.95391, 0.44301, 0.93005, 0.42458, 0.90092, 0.4098, 0.87756, 0.3531, 0.86126, 0.31071, 0.8462, 0.27287, 0.82849, 0.23944, 0.82863, 0.21336, 0.82462, 0.19205, 0.80713, 0.1763, 0.78103, 0.17544, 0.7546, 0.15225, 0.73275, 0.12704, 0.75153, 0.09896, 0.78407, 0.07158, 0.81002, 0.02474, 0.81038, 0.00898, 0.79541, 0.00014, 0.76864, 0.0126, 0.74387, 0.02727, 0.723, 0.06373, 0.71112, 0.1024, 0.70314, 0.11577, 0.6871, 0.11344, 0.66789, 0.10013, 0.64319, 0.07288, 0.63049, 0.0481, 0.60493, 0.0395, 0.57932, 0.04017, 0.53824, 0.05265, 0.51498, 0.04012, 0.49584, 0.02776, 0.45571, 0.02776, 0.39855, 0.042, 0.36049, 0.07071, 0.32161, 0.10651, 0.29848, 0.14993, 0.28593, 0.17204, 0.25511, 0.157, 0.23649, 0.12839, 0.21904, 0.09915, 0.19856, 0.07437, 0.1592, 0.07434, 0.10411, 0.09293, 0.06764, 0.12711, 0.04525, 0.7637, 0.87399, 0.51839, 0.89337, 0.06159, 0.75434, 0.88018, 0.68481, 0.82945, 0.40232, 0.45472, 0.47982, 0.74434, 0.1825], "triangles": [95, 96, 122, 11, 12, 123, 123, 14, 15, 111, 112, 0, 122, 96, 101, 77, 78, 122, 79, 122, 78, 122, 79, 80, 96, 97, 99, 97, 98, 99, 87, 88, 119, 116, 0, 113, 115, 116, 114, 3, 0, 2, 2, 0, 1, 117, 50, 51, 117, 52, 53, 117, 51, 52, 120, 34, 35, 120, 35, 36, 37, 120, 36, 38, 120, 37, 38, 39, 120, 120, 39, 40, 117, 58, 59, 69, 118, 68, 71, 72, 118, 70, 71, 118, 86, 119, 85, 86, 87, 119, 69, 70, 118, 57, 58, 117, 56, 57, 117, 59, 60, 117, 56, 117, 55, 40, 41, 120, 55, 117, 54, 117, 53, 54, 123, 13, 14, 116, 113, 114, 85, 119, 84, 119, 88, 89, 119, 89, 90, 3, 4, 0, 111, 0, 4, 0, 112, 113, 123, 12, 13, 41, 42, 120, 43, 44, 31, 43, 31, 42, 42, 32, 120, 83, 94, 95, 120, 33, 34, 120, 32, 33, 45, 122, 44, 101, 96, 99, 31, 122, 30, 29, 30, 122, 122, 20, 21, 122, 121, 29, 21, 22, 121, 121, 122, 21, 29, 121, 28, 101, 108, 122, 108, 103, 107, 106, 107, 105, 101, 103, 108, 105, 107, 103, 28, 121, 27, 101, 102, 103, 105, 103, 104, 20, 9, 123, 20, 122, 9, 9, 10, 123, 10, 11, 123, 108, 109, 122, 109, 6, 122, 6, 7, 122, 122, 8, 9, 122, 7, 8, 27, 121, 26, 26, 121, 25, 121, 24, 25, 121, 23, 24, 121, 22, 23, 20, 123, 19, 19, 123, 18, 109, 110, 6, 18, 123, 17, 110, 5, 6, 110, 4, 5, 4, 110, 111, 123, 16, 17, 123, 15, 16, 31, 44, 122, 101, 99, 100, 65, 74, 122, 122, 74, 75, 64, 65, 122, 76, 122, 75, 47, 64, 122, 77, 122, 76, 122, 80, 81, 81, 82, 122, 47, 122, 46, 82, 83, 122, 46, 122, 45, 42, 31, 32, 68, 118, 67, 72, 73, 118, 60, 61, 117, 67, 118, 66, 73, 74, 118, 66, 118, 65, 61, 62, 117, 65, 118, 74, 62, 48, 117, 117, 49, 50, 117, 48, 49, 62, 63, 48, 48, 63, 47, 63, 64, 47, 93, 119, 92, 119, 93, 84, 90, 91, 119, 119, 91, 92, 84, 93, 83, 93, 94, 83, 122, 83, 95], "vertices": [2, 44, -90.46, 143.76, 0.02155, 45, -90.46, 154.44, 0.97845, 2, 44, -78.96, 143.56, 0.00946, 45, -78.96, 154.24, 0.99054, 2, 44, -69.06, 140.41, 0.153, 45, -69.06, 151.08, 0.847, 2, 44, -62.08, 135.28, 0.39795, 45, -62.08, 145.96, 0.60205, 2, 44, -57.73, 128.96, 0.70584, 45, -57.73, 139.64, 0.29416, 2, 44, -54.31, 121.28, 0.93146, 45, -54.31, 131.95, 0.06854, 2, 44, -51.33, 116.34, 0.95362, 45, -51.33, 127.01, 0.04638, 2, 44, -14.95, 122.1, 0.7559, 45, -14.95, 132.78, 0.2441, 2, 44, 24.35, 121.97, 0.76179, 45, 24.35, 132.65, 0.23821, 2, 44, 61.21, 111.18, 0.74063, 45, 61.21, 121.86, 0.25937, 2, 44, 64.89, 117.25, 0.64513, 45, 64.89, 127.93, 0.35487, 2, 44, 70.31, 124.39, 0.36729, 45, 70.31, 135.07, 0.63271, 2, 44, 80.47, 126.67, 0.19886, 45, 80.47, 137.34, 0.80114, 2, 44, 95.66, 125.92, 0.10171, 45, 95.66, 136.6, 0.89829, 2, 44, 108.56, 122.02, 0.19647, 45, 108.56, 132.69, 0.80353, 2, 44, 117.62, 114.78, 0.36288, 45, 117.62, 125.46, 0.63712, 2, 44, 121.49, 104.86, 0.46208, 45, 121.49, 115.54, 0.53792, 2, 44, 121.69, 92.1, 0.59174, 45, 121.69, 102.78, 0.40826, 2, 44, 114.64, 85.44, 0.74078, 45, 114.64, 96.12, 0.25922, 2, 44, 106.9, 81.54, 0.86799, 45, 106.9, 92.21, 0.13201, 1, 44, 103.35, 77.75, 1, 1, 44, 109.67, 66.42, 1, 2, 44, 119.96, 66.84, 0.98349, 45, 119.96, 77.51, 0.01651, 2, 44, 132.78, 58.11, 0.81787, 45, 132.78, 68.78, 0.18213, 2, 44, 141.05, 47.77, 0.70315, 45, 141.05, 58.44, 0.29685, 2, 44, 146.38, 34.2, 0.62876, 45, 146.38, 44.88, 0.37124, 2, 44, 146.29, 22, 0.60882, 45, 146.29, 32.68, 0.39118, 2, 44, 144.11, 8.19, 0.63067, 45, 144.11, 18.86, 0.36933, 2, 44, 136.31, 1.81, 0.74414, 45, 136.31, 12.49, 0.25586, 2, 44, 126.93, -2.41, 0.88743, 45, 126.93, 8.27, 0.11257, 1, 44, 125.67, -20.51, 1, 2, 44, 121.63, -39.03, 0.92415, 45, 121.63, -28.36, 0.07585, 2, 44, 129.26, -42.12, 0.85626, 45, 129.26, -31.44, 0.14374, 2, 44, 140.76, -47.64, 0.65141, 45, 140.76, -36.96, 0.34859, 2, 44, 147.81, -54, 0.45337, 45, 147.81, -43.33, 0.54663, 2, 44, 153.4, -64.06, 0.22414, 45, 153.4, -53.39, 0.77586, 2, 44, 154.61, -72.28, 0.12863, 45, 154.61, -61.61, 0.87137, 2, 44, 152.64, -81.49, 0.06435, 45, 152.64, -70.82, 0.93565, 2, 44, 146.18, -87.13, 0.0759, 45, 146.18, -76.45, 0.9241, 2, 44, 138.88, -88.32, 0.11319, 45, 138.88, -77.64, 0.88681, 2, 44, 131.77, -85.67, 0.22246, 45, 131.77, -75, 0.77754, 2, 44, 126.5, -77.3, 0.41853, 45, 126.5, -66.62, 0.58147, 2, 44, 121.97, -65.4, 0.66246, 45, 121.97, -54.72, 0.33754, 2, 44, 114.11, -56.28, 0.99426, 45, 114.11, -45.61, 0.00574, 2, 44, 109.4, -62.28, 0.99314, 45, 109.4, -51.6, 0.00686, 2, 44, 104.08, -70.39, 0.99213, 45, 104.08, -59.71, 0.00787, 2, 44, 98.24, -77.33, 0.99754, 45, 98.24, -66.65, 0.00246, 2, 44, 94.36, -89.81, 0.60983, 45, 94.36, -79.13, 0.39017, 2, 44, 97.33, -95.07, 0.67939, 45, 97.33, -84.39, 0.32061, 2, 44, 103.43, -101.07, 0.58167, 45, 103.43, -90.39, 0.41833, 2, 44, 109.54, -105.3, 0.43988, 45, 109.54, -94.63, 0.56012, 2, 44, 115.81, -112.43, 0.3264, 45, 115.81, -101.75, 0.6736, 2, 44, 121.25, -119, 0.20757, 45, 121.25, -108.32, 0.79243, 2, 44, 124.38, -126.45, 0.2256, 45, 124.38, -115.78, 0.7744, 2, 44, 124.42, -132.77, 0.33257, 45, 124.42, -122.1, 0.66743, 2, 44, 123.55, -143.88, 0.27292, 45, 123.55, -133.21, 0.72708, 2, 44, 118.59, -152.98, 0.13068, 45, 118.59, -142.31, 0.86932, 1, 45, 109.99, -150.11, 1, 1, 45, 96.24, -151.15, 1, 2, 44, 85.73, -158.03, 0.09602, 45, 85.73, -147.35, 0.90398, 2, 44, 78.15, -145.04, 0.39368, 45, 78.15, -134.37, 0.60632, 2, 44, 73.51, -130.78, 0.70124, 45, 73.51, -120.11, 0.29876, 2, 44, 71.94, -119.42, 0.86966, 45, 71.94, -108.74, 0.13034, 2, 44, 68.6, -110.64, 0.95079, 45, 68.6, -99.96, 0.04921, 2, 44, 61.39, -108.45, 0.97473, 45, 61.39, -97.77, 0.02527, 2, 44, 55.07, -112.16, 0.95012, 45, 55.07, -101.49, 0.04988, 2, 44, 53.57, -125.98, 0.83978, 45, 53.57, -115.3, 0.16022, 2, 44, 51.29, -136.12, 0.7483, 45, 51.29, -125.44, 0.2517, 2, 44, 43.94, -150.35, 0.50798, 45, 43.94, -139.67, 0.49202, 2, 44, 33.03, -161.53, 0.20256, 45, 33.03, -150.85, 0.79744, 2, 44, 21.01, -165.66, 0.00552, 45, 21.01, -154.98, 0.99448, 1, 45, 8.04, -153.46, 1, 2, 44, -2.08, -151.12, 0.29991, 45, -2.08, -140.44, 0.70009, 2, 44, -8.02, -136, 0.61942, 45, -8.02, -125.33, 0.38058, 2, 44, -12.78, -126.03, 0.74609, 45, -12.78, -115.35, 0.25391, 2, 44, -31.03, -119.37, 0.81636, 45, -31.03, -108.69, 0.18364, 2, 44, -44.69, -117.31, 0.63451, 45, -44.69, -106.63, 0.36549, 2, 44, -56.87, -112.04, 0.59263, 45, -56.87, -101.36, 0.40737, 2, 44, -67.63, -113.63, 0.49938, 45, -67.63, -102.96, 0.50062, 2, 44, -76.03, -113.1, 0.44983, 45, -76.03, -102.43, 0.55017, 2, 44, -82.89, -105.42, 0.5575, 45, -82.89, -94.75, 0.4425, 2, 44, -87.96, -92.4, 0.81245, 45, -87.96, -81.72, 0.18755, 2, 44, -88.24, -80.61, 0.9861, 45, -88.24, -69.93, 0.0139, 2, 44, -95.71, -73.18, 0.99035, 45, -95.71, -62.5, 0.00965, 2, 44, -103.83, -87.19, 0.52724, 45, -103.83, -76.51, 0.47276, 1, 45, -112.87, -96.22, 1, 1, 45, -121.68, -104.97, 1, 1, 45, -136.77, -105.09, 1, 2, 44, -141.84, -109.31, 0.08531, 45, -141.84, -98.63, 0.91469, 2, 44, -144.69, -96.14, 0.33535, 45, -144.69, -85.46, 0.66465, 2, 44, -140.67, -85.7, 0.46148, 45, -140.67, -75.02, 0.53852, 2, 44, -135.95, -77.08, 0.55661, 45, -135.95, -66.41, 0.44339, 2, 44, -124.21, -70.98, 0.68332, 45, -124.21, -60.3, 0.31668, 2, 44, -111.76, -66.77, 0.77501, 45, -111.76, -56.09, 0.22499, 2, 44, -107.46, -60.29, 0.83963, 45, -107.46, -49.62, 0.16037, 2, 44, -108.21, -51.16, 0.23692, 45, -108.21, -40.48, 0.76308, 2, 44, -112.49, -52.79, 0.39981, 45, -112.49, -42.12, 0.60019, 2, 44, -121.27, -49.57, 0.33627, 45, -121.27, -38.89, 0.66373, 2, 44, -129.25, -38.22, 0.50107, 45, -129.25, -27.54, 0.49893, 2, 44, -132.01, -27.67, 0.61634, 45, -132.01, -17, 0.38366, 2, 44, -131.8, -13.11, 0.65964, 45, -131.8, -2.44, 0.34036, 2, 44, -127.78, -5.36, 0.65438, 45, -127.78, 5.31, 0.34562, 2, 44, -131.82, 1.43, 0.6746, 45, -131.82, 12.1, 0.3254, 2, 44, -135.8, 18.83, 0.90838, 45, -135.8, 29.5, 0.09162, 2, 44, -135.79, 36.43, 0.80863, 45, -135.79, 47.11, 0.19137, 2, 44, -131.21, 47.68, 0.71293, 45, -131.21, 58.35, 0.28707, 2, 44, -121.97, 60.35, 0.68699, 45, -121.97, 71.02, 0.31301, 2, 44, -110.44, 69.7, 0.78083, 45, -110.44, 80.37, 0.21917, 2, 44, -96.46, 76.64, 0.94418, 45, -96.46, 87.31, 0.05582, 2, 44, -89.34, 82.55, 0.67426, 45, -89.34, 93.22, 0.32574, 2, 44, -94.18, 88.9, 0.67915, 45, -94.18, 99.58, 0.32085, 2, 44, -103.39, 92.35, 0.53234, 45, -103.39, 103.02, 0.46766, 2, 44, -112.81, 99.57, 0.55156, 45, -112.81, 110.24, 0.44844, 2, 44, -120.79, 108.47, 0.28889, 45, -120.79, 119.15, 0.71111, 2, 44, -120.8, 122.84, 0.03582, 45, -120.8, 133.52, 0.96418, 1, 45, -114.81, 145.21, 1, 1, 45, -103.8, 152.76, 1, 2, 44, 101.18, -125.37, 0.71328, 45, 101.18, -114.69, 0.28672, 2, 44, 22.19, -131.76, 0.72168, 45, 22.19, -121.09, 0.27832, 2, 44, -124.9, -88.81, 0.48636, 45, -124.9, -78.13, 0.51364, 2, 44, 138.69, -65.46, 0.48188, 45, 138.69, -54.78, 0.51812, 2, 44, 122.35, 36.82, 0.90859, 45, 122.35, 47.5, 0.09141, 2, 44, 1.69, 0.84, 0.31422, 45, 1.69, 11.52, 0.68578, 2, 44, 94.94, 109.37, 0.81578, 45, 94.94, 120.04, 0.18422], "hull": 117, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 34, 36, 58, 60, 60, 62, 72, 74, 152, 154, 172, 174, 174, 176, 176, 178, 210, 212, 218, 220, 224, 226, 226, 228, 228, 230, 230, 232, 166, 168, 168, 170, 170, 172, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 200, 202, 202, 204, 196, 198, 198, 200, 158, 160, 160, 162, 154, 156, 156, 158, 162, 164, 164, 166, 146, 148, 142, 144, 144, 146, 140, 142, 134, 136, 132, 134, 136, 138, 138, 140, 128, 130, 130, 132, 126, 128, 122, 124, 124, 126, 118, 120, 120, 122, 114, 116, 116, 118, 110, 112, 112, 114, 108, 110, 178, 180, 180, 182, 102, 104, 100, 102, 94, 96, 104, 106, 106, 108, 96, 98, 98, 100, 90, 92, 92, 94, 86, 88, 88, 90, 82, 84, 84, 86, 74, 76, 76, 78, 78, 80, 80, 82, 68, 70, 70, 72, 62, 64, 64, 66, 66, 68, 54, 56, 56, 58, 50, 52, 52, 54, 46, 48, 48, 50, 44, 46, 244, 60, 40, 42, 42, 44, 36, 38, 38, 40, 30, 32, 32, 34, 26, 28, 28, 30, 18, 20, 20, 22, 40, 244, 148, 150, 150, 152, 156, 244, 160, 244, 162, 244, 158, 244, 6, 8, 8, 10, 2, 4, 4, 6, 2, 0, 0, 232, 220, 222, 222, 224, 216, 218, 212, 214, 214, 216, 208, 210, 206, 208, 204, 206, 244, 202], "width": 322, "height": 337}}, "blot_drop2": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop3": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop4": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop5": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "blot_drop6": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop7": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop8": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s1": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s2": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "body": {"body": {"type": "mesh", "uvs": [0.57099, 0.03229, 0.59905, 0.05171, 0.62803, 0.08788, 0.657, 0.12404, 0.69944, 0.19892, 0.72361, 0.24157, 0.75908, 0.2928, 0.78285, 0.29375, 0.80383, 0.25492, 0.8198, 0.20566, 0.84075, 0.15875, 0.86401, 0.12937, 0.88918, 0.12024, 0.91332, 0.1289, 0.92774, 0.14311, 0.947, 0.17439, 0.9674, 0.22983, 0.98304, 0.29807, 0.99365, 0.37133, 0.9976, 0.41636, 0.9976, 0.57983, 0.98903, 0.6635, 0.97515, 0.73269, 0.9557, 0.79573, 0.93574, 0.83724, 0.91482, 0.85886, 0.88736, 0.86349, 0.86539, 0.86207, 0.84003, 0.83008, 0.80997, 0.75842, 0.79362, 0.72166, 0.78096, 0.70329, 0.7625, 0.70329, 0.73401, 0.76944, 0.69918, 0.84295, 0.67229, 0.88889, 0.64489, 0.92932, 0.60631, 0.96055, 0.57249, 0.98169, 0.55014, 0.99018, 0.5001, 0.9994, 0.44761, 0.99148, 0.38383, 0.95522, 0.35126, 0.91233, 0.31869, 0.86945, 0.30314, 0.84076, 0.26637, 0.77296, 0.23868, 0.70684, 0.2197, 0.70863, 0.20175, 0.72114, 0.1761, 0.79261, 0.15353, 0.83907, 0.13866, 0.8623, 0.09827, 0.86007, 0.04778, 0.80512, 0.0247, 0.72471, 0.00572, 0.59784, 0, 0.52637, 1e-05, 0.42239, 0.0162, 0.28075, 0.0397, 0.18771, 0.08479, 0.12686, 0.13235, 0.12331, 0.15938, 0.15554, 0.17841, 0.19486, 0.19872, 0.2476, 0.21602, 0.28678, 0.23678, 0.28979, 0.26446, 0.25966, 0.29344, 0.20842, 0.32242, 0.15719, 0.35875, 0.10596, 0.39507, 0.05473, 0.42795, 0.02736, 0.45959, 0.01286, 0.50096, 0, 0.54294, 0.01286], "triangles": [43, 71, 42, 42, 71, 72, 43, 70, 71, 37, 2, 36, 36, 3, 35, 36, 2, 3, 4, 35, 3, 38, 74, 75, 38, 39, 74, 38, 1, 37, 1, 38, 0, 37, 1, 2, 76, 0, 38, 76, 38, 75, 39, 40, 74, 74, 40, 41, 74, 41, 73, 41, 42, 73, 73, 42, 72, 12, 25, 26, 26, 27, 28, 23, 24, 25, 12, 23, 25, 12, 26, 10, 10, 26, 9, 22, 23, 17, 15, 16, 23, 15, 23, 12, 28, 29, 9, 14, 15, 13, 28, 9, 26, 13, 15, 12, 12, 10, 11, 30, 8, 29, 29, 8, 9, 21, 22, 20, 17, 23, 16, 18, 22, 17, 20, 22, 19, 19, 22, 18, 30, 7, 8, 52, 53, 51, 50, 51, 53, 53, 63, 50, 63, 53, 62, 53, 54, 62, 62, 55, 61, 62, 54, 55, 59, 60, 61, 49, 64, 65, 49, 50, 64, 50, 63, 64, 61, 55, 56, 59, 61, 56, 49, 66, 48, 49, 65, 66, 57, 58, 56, 56, 58, 59, 34, 35, 4, 33, 34, 4, 33, 4, 5, 32, 5, 6, 32, 33, 5, 31, 7, 30, 31, 32, 7, 32, 6, 7, 44, 70, 43, 44, 45, 70, 46, 69, 45, 45, 69, 70, 47, 68, 46, 46, 68, 69, 48, 67, 47, 48, 66, 67, 47, 67, 68], "vertices": [5, 4, -58.18, -33.89, 0.00088, 5, -17.53, 35.61, 0.0364, 7, -146.43, -33.89, 6e-05, 8, -114.87, 35.61, 0.00569, 3, 23.88, 35.42, 0.95697, 5, 4, -67.19, -31.69, 0.00102, 5, -8.37, 34.24, 0.09282, 7, -155.43, -31.69, 1e-05, 8, -105.7, 34.24, 0.01452, 3, 32.97, 33.61, 0.89163, 4, 4, -76.42, -27.92, 0.0021, 5, 1.17, 31.32, 0.19886, 8, -96.17, 31.32, 0.03112, 3, 42.36, 30.25, 0.76792, 4, 4, -85.65, -24.16, 0.00387, 5, 10.71, 28.41, 0.36682, 8, -86.63, 28.41, 0.0574, 3, 51.74, 26.88, 0.57191, 4, 4, -99.09, -16.61, 0.004, 5, 24.77, 22.1, 0.50394, 8, -72.56, 22.1, 0.11954, 3, 65.49, 19.92, 0.37252, 4, 4, -106.74, -12.31, 0.00363, 5, 32.78, 18.51, 0.58988, 8, -64.55, 18.51, 0.19911, 3, 73.32, 15.95, 0.20738, 4, 4, -118.02, -7.05, 0.00229, 5, 44.49, 14.3, 0.50057, 8, -52.85, 14.3, 0.41693, 3, 84.82, 11.19, 0.08021, 4, 4, -125.71, -6.63, 0.00104, 5, 52.19, 14.58, 0.33738, 8, -45.15, 14.58, 0.63913, 3, 92.52, 11.1, 0.02244, 4, 4, -132.66, -9.94, 0.00029, 5, 58.8, 18.51, 0.15321, 8, -38.53, 18.51, 0.84291, 3, 99.32, 14.71, 0.00359, 4, 4, -138.02, -14.3, 6e-05, 5, 63.76, 23.33, 0.05671, 8, -33.58, 23.33, 0.94286, 3, 104.49, 19.29, 0.00037, 1, 8, -27.01, 28.01, 1, 1, 8, -19.61, 31.09, 1, 1, 8, -11.51, 32.33, 1, 1, 8, -3.65, 31.9, 1, 1, 8, 1.07, 30.8, 1, 1, 8, 7.45, 28.19, 1, 1, 8, 14.29, 23.35, 1, 1, 8, 19.66, 17.25, 1, 1, 8, 23.41, 10.61, 1, 1, 8, 24.89, 6.49, 1, 1, 8, 25.61, -8.7, 1, 1, 8, 23.21, -16.6, 1, 1, 8, 19.02, -23.24, 1, 1, 8, 13, -29.4, 1, 1, 8, 6.73, -33.56, 1, 1, 8, 0.05, -35.89, 1, 1, 8, -8.82, -36.74, 1, 1, 8, -15.93, -36.95, 1, 1, 8, -24.28, -34.37, 1, 3, 4, -132.62, 36.92, 7e-05, 5, 63.01, -28.17, 0.30857, 8, -34.33, -28.17, 0.69136, 3, 4, -127.48, 33.28, 0.00043, 5, 57.56, -25.01, 0.57981, 8, -39.78, -25.01, 0.41975, 3, 4, -123.45, 31.4, 0.00156, 5, 53.38, -23.5, 0.78445, 8, -43.96, -23.5, 0.21399, 3, 4, -117.48, 31.14, 0.00402, 5, 47.4, -23.78, 0.90956, 8, -49.93, -23.78, 0.08642, 3, 4, -107.99, 36.89, 0.01757, 5, 38.48, -30.36, 0.92801, 8, -58.86, -30.36, 0.05441, 4, 4, -96.42, 43.23, 0.01218, 5, 27.53, -37.73, 0.64296, 8, -69.81, -37.73, 0.0377, 3, 65.41, -39.98, 0.30717, 4, 4, -87.53, 47.12, 0.00752, 5, 19.03, -42.41, 0.39727, 8, -78.31, -42.41, 0.02329, 3, 56.7, -44.25, 0.57191, 5, 4, -78.5, 50.49, 0.00412, 5, 10.34, -46.58, 0.21537, 7, -166.75, 50.49, 0, 8, -87, -46.58, 0.01263, 3, 47.82, -48.01, 0.76787, 5, 4, -65.89, 52.86, 0.00241, 5, -2.01, -50.08, 0.10053, 7, -154.13, 52.86, 3e-05, 8, -99.34, -50.08, 0.00589, 3, 35.32, -50.91, 0.89112, 5, 4, -54.85, 54.35, 0.00376, 5, -12.86, -52.56, 0.0395, 7, -143.1, 54.35, 0.00021, 8, -110.2, -52.56, 0.00231, 3, 24.36, -52.88, 0.95422, 5, 4, -47.58, 54.82, 0.01257, 5, -20.06, -53.7, 0.01287, 7, -135.83, 54.82, 0.00084, 8, -117.39, -53.7, 0.00074, 3, 17.12, -53.67, 0.97297, 5, 4, -31.35, 54.98, 0.03883, 5, -36.21, -55.32, 0.00411, 7, -119.6, 54.98, 0.00265, 8, -133.55, -55.32, 0.00018, 3, 0.91, -54.53, 0.95422, 5, 4, -14.39, 53.51, 0.09889, 5, -53.23, -55.39, 0.00319, 7, -102.64, 53.51, 0.00677, 8, -150.57, -55.39, 3e-05, 3, -16.1, -53.79, 0.89112, 5, 4, 6.11, 49.25, 0.21185, 5, -74.03, -53, 0.00577, 7, -82.14, 49.25, 0.0145, 8, -171.37, -53, 0, 3, -36.77, -50.42, 0.76787, 4, 4, 16.48, 44.81, 0.39077, 5, -84.76, -49.52, 0.01056, 7, -71.77, 44.81, 0.02675, 3, -47.32, -46.43, 0.57191, 4, 4, 26.85, 40.37, 0.63244, 5, -95.49, -46.04, 0.01709, 7, -61.4, 40.37, 0.0433, 3, -57.87, -42.44, 0.30717, 3, 4, 31.77, 37.48, 0.91284, 5, -100.65, -43.61, 0.02467, 7, -56.48, 37.48, 0.06249, 3, 4, 43.4, 30.67, 0.87294, 5, -112.85, -37.88, 0.0097, 7, -44.85, 30.67, 0.11736, 3, 4, 52.1, 24.14, 0.81311, 5, -122.11, -32.16, 0.00171, 7, -36.15, 24.14, 0.18519, 3, 4, 58.25, 24.04, 0.60442, 5, -128.24, -32.62, 0.00052, 7, -30, 24.04, 0.39506, 3, 4, 64.11, 24.95, 0.3209, 5, -133.99, -34.06, 9e-05, 7, -24.14, 24.95, 0.67901, 1, 7, -15.55, 31.23, 1, 1, 7, -8.06, 35.23, 1, 1, 7, -3.15, 37.18, 1, 1, 7, 9.91, 36.41, 1, 1, 7, 26.04, 30.6, 1, 1, 7, 33.19, 22.8, 1, 1, 7, 38.82, 10.75, 1, 1, 7, 40.38, 4.03, 1, 1, 7, 39.96, -5.63, 1, 1, 7, 34.15, -18.56, 1, 1, 7, 26.17, -26.88, 1, 1, 7, 11.33, -31.9, 1, 1, 7, -4.08, -31.57, 1, 1, 7, -12.7, -28.19, 1, 1, 7, -18.7, -24.27, 1, 2, 4, 63.19, -19.09, 0.17284, 7, -25.06, -19.09, 0.82716, 2, 4, 57.74, -15.21, 0.38272, 7, -30.5, -15.21, 0.61728, 2, 4, 51.04, -14.64, 0.61728, 7, -37.21, -14.64, 0.38272, 2, 4, 41.95, -17.05, 0.82716, 7, -46.29, -17.05, 0.17284, 3, 4, 32.37, -21.4, 0.8843, 5, -106.58, 14.98, 0.01114, 7, -55.88, -21.4, 0.10456, 4, 4, 22.78, -25.76, 0.61267, 5, -97.43, 20.19, 0.00772, 7, -65.47, -25.76, 0.07244, 3, -56.66, 23.8, 0.30717, 4, 4, 10.82, -30.01, 0.37856, 5, -85.9, 25.5, 0.00477, 7, -77.43, -30.01, 0.04476, 3, -44.89, 28.56, 0.57191, 4, 4, -1.15, -34.26, 0.20523, 5, -74.37, 30.82, 0.00259, 7, -89.39, -34.26, 0.02427, 3, -33.12, 33.33, 0.76792, 5, 4, -11.9, -36.35, 0.09579, 5, -63.85, 33.87, 0.00125, 7, -100.15, -36.35, 0.01133, 8, -161.19, 33.87, 1e-05, 3, -22.47, 35.87, 0.89163, 5, 4, -22.2, -37.25, 0.03756, 5, -53.67, 35.7, 0.00095, 7, -110.45, -37.25, 0.00444, 8, -151.01, 35.7, 7e-05, 3, -12.22, 37.22, 0.95697, 5, 4, -35.64, -37.87, 0.01198, 5, -40.34, 37.53, 0.00298, 7, -123.89, -37.87, 0.00141, 8, -137.68, 37.53, 0.00044, 3, 1.18, 38.42, 0.98318, 5, 4, -49.18, -36.08, 0.00304, 5, -26.7, 36.98, 0.01162, 7, -137.43, -36.08, 0.00035, 8, -124.04, 36.98, 0.00181, 3, 14.79, 37.22, 0.98318], "hull": 77, "edges": [116, 118, 118, 120, 120, 122, 130, 132, 132, 134, 134, 136, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 82, 84, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 114, 116, 112, 114, 140, 142, 142, 144, 84, 86, 86, 88, 136, 138, 138, 140, 88, 90, 90, 92, 144, 146, 146, 148, 148, 150, 150, 152, 78, 80, 80, 82, 2, 0, 0, 152, 2, 4, 4, 6, 6, 8, 8, 10, 22, 24, 24, 26, 50, 52, 52, 54, 122, 124, 124, 126, 126, 128, 128, 130], "width": 324, "height": 93}}, "body_outline": {"body_outline": {"type": "mesh", "uvs": [0.60349, 0.05009, 0.64769, 0.09861, 0.69717, 0.17255, 0.73631, 0.23263, 0.75804, 0.25574, 0.77978, 0.22801, 0.81674, 0.1402, 0.85225, 0.11016, 0.89872, 0.11254, 0.93707, 0.15457, 0.96212, 0.22214, 0.97963, 0.29806, 0.99153, 0.3896, 0.9958, 0.44765, 0.9958, 0.57046, 0.97613, 0.70665, 0.95722, 0.77587, 0.93061, 0.83392, 0.89839, 0.86964, 0.86475, 0.88081, 0.83748, 0.86518, 0.81018, 0.83169, 0.78497, 0.76917, 0.77096, 0.73791, 0.75416, 0.73345, 0.72827, 0.79819, 0.69257, 0.85847, 0.64906, 0.91307, 0.60775, 0.95103, 0.57135, 0.97112, 0.52232, 0.98884, 0.453, 0.9866, 0.40321, 0.95773, 0.34999, 0.91307, 0.31498, 0.86395, 0.28417, 0.81483, 0.25686, 0.76124, 0.23725, 0.72775, 0.22465, 0.73222, 0.20364, 0.79474, 0.17981, 0.83939, 0.1497, 0.87288, 0.09095, 0.86842, 0.07071, 0.83331, 0.04013, 0.75506, 0.01246, 0.63621, 0, 0.53127, 0, 0.47294, 0.01088, 0.31812, 0.04349, 0.18873, 0.07516, 0.13245, 0.11773, 0.10745, 0.16523, 0.11479, 0.19784, 0.15869, 0.2232, 0.23032, 0.23986, 0.25343, 0.2703, 0.228, 0.3203, 0.14945, 0.36016, 0.08475, 0.40508, 0.04547, 0.46219, 0, 0.54262, 0], "triangles": [33, 57, 58, 26, 27, 2, 31, 60, 30, 30, 60, 61, 29, 30, 61, 2, 27, 1, 28, 0, 1, 28, 1, 27, 0, 29, 61, 0, 28, 29, 32, 58, 59, 33, 58, 32, 60, 31, 32, 32, 59, 60, 10, 11, 8, 12, 16, 11, 15, 12, 13, 15, 13, 14, 23, 4, 5, 7, 22, 5, 23, 5, 22, 6, 7, 5, 20, 21, 22, 7, 8, 17, 10, 8, 9, 8, 11, 16, 12, 15, 16, 20, 22, 7, 8, 16, 17, 20, 7, 18, 17, 18, 7, 19, 20, 18, 45, 46, 47, 49, 47, 48, 49, 45, 47, 55, 38, 54, 50, 44, 45, 49, 50, 45, 52, 54, 51, 51, 44, 50, 53, 54, 52, 51, 38, 39, 38, 51, 54, 51, 40, 44, 40, 43, 44, 51, 39, 40, 41, 42, 43, 40, 41, 43, 24, 3, 4, 24, 4, 23, 25, 2, 3, 25, 3, 24, 26, 2, 25, 37, 38, 55, 56, 37, 55, 36, 37, 56, 35, 56, 57, 36, 56, 35, 34, 35, 57, 34, 57, 33], "vertices": [5, 4, -70.31, -37.06, 0.00112, 5, -5.74, 39.87, 0.102, 7, -158.56, -37.06, 0, 8, -103.08, 39.87, 0.01596, 3, 35.85, 39.11, 0.88092, 4, 4, -85.01, -31.27, 0.00329, 5, 9.42, 35.44, 0.31154, 8, -87.91, 35.44, 0.04875, 3, 50.79, 33.97, 0.63643, 4, 4, -101.38, -22.72, 0.00399, 5, 26.5, 28.4, 0.49506, 8, -70.84, 28.4, 0.11551, 3, 67.52, 26.13, 0.38543, 4, 4, -114.32, -15.79, 0.00305, 5, 40.02, 22.67, 0.55113, 8, -57.32, 22.67, 0.29361, 3, 80.75, 19.76, 0.15221, 4, 4, -121.56, -13.02, 0.00177, 5, 47.47, 20.57, 0.43261, 8, -49.87, 20.57, 0.50946, 3, 88.09, 17.31, 0.05616, 4, 4, -129.02, -15.64, 0.00037, 5, 54.67, 23.86, 0.17292, 8, -42.67, 23.86, 0.8211, 3, 95.44, 20.25, 0.0056, 1, 8, -30.63, 33.75, 1, 1, 8, -18.79, 37.49, 1, 1, 8, -3.09, 37.99, 1, 1, 8, 10.07, 34.15, 1, 1, 8, 18.87, 27.4, 1, 1, 8, 25.16, 19.64, 1, 1, 8, 29.64, 10.14, 1, 1, 8, 31.37, 4.06, 1, 1, 8, 31.99, -8.94, 1, 1, 8, 26.03, -23.68, 1, 1, 8, 20, -31.31, 1, 1, 8, 11.3, -37.88, 1, 1, 8, 0.61, -42.18, 1, 1, 8, -10.7, -43.9, 1, 1, 8, -19.98, -42.68, 1, 3, 4, -136.52, 48.73, 1e-05, 5, 67.97, -39.58, 0.0418, 8, -29.37, -39.58, 0.95819, 3, 4, -128.3, 41.74, 0.00012, 5, 59.14, -33.36, 0.34057, 8, -38.19, -33.36, 0.65932, 3, 4, -123.71, 38.23, 0.00077, 5, 54.26, -30.27, 0.64015, 8, -43.08, -30.27, 0.35908, 3, 4, -118.06, 37.51, 0.00744, 5, 48.56, -30.07, 0.91423, 8, -48.78, -30.07, 0.07833, 4, 4, -109.02, 43.99, 0.01655, 5, 40.15, -37.34, 0.8738, 8, -57.19, -37.34, 0.05123, 3, 78.03, -40.19, 0.05841, 4, 4, -96.69, 49.85, 0.01102, 5, 28.4, -44.3, 0.58194, 8, -68.94, -44.3, 0.03412, 3, 65.96, -46.58, 0.37292, 5, 4, -81.74, 55, 0.00464, 5, 13.98, -50.78, 0.24302, 7, -169.99, 55, 0, 8, -83.36, -50.78, 0.01425, 3, 51.26, -52.37, 0.73809, 5, 4, -67.62, 58.41, 0.0025, 5, 0.23, -55.46, 0.10665, 7, -155.87, 58.41, 3e-05, 8, -97.11, -55.46, 0.00625, 3, 37.29, -56.39, 0.88456, 5, 4, -55.24, 60.01, 0.00376, 5, -11.96, -58.17, 0.0395, 7, -143.49, 60.01, 0.00021, 8, -109.3, -58.17, 0.00231, 3, 24.99, -58.52, 0.95422, 5, 4, -38.6, 61.17, 0.0272, 5, -28.43, -60.83, 0.00799, 7, -126.85, 61.17, 0.00185, 8, -125.76, -60.83, 0.00043, 3, 8.42, -60.4, 0.96253, 5, 4, -15.2, 59.92, 0.09408, 5, -51.84, -61.7, 0.00326, 7, -103.45, 59.92, 0.00644, 8, -149.18, -61.7, 4e-05, 3, -15.01, -60.16, 0.89618, 5, 4, 1.48, 56.14, 0.17983, 5, -68.8, -59.45, 0.00504, 7, -86.77, 56.14, 0.01231, 8, -166.14, -59.45, 1e-05, 3, -31.84, -57.1, 0.80281, 4, 4, 19.25, 50.63, 0.39612, 5, -86.99, -55.57, 0.01071, 7, -69, 50.63, 0.02712, 3, -49.83, -52.37, 0.56606, 4, 4, 30.84, 44.92, 0.68867, 5, -99.06, -50.93, 0.01861, 7, -57.4, 44.92, 0.04714, 3, -61.66, -47.16, 0.24558, 3, 4, 41.02, 39.27, 0.89188, 5, -109.71, -46.22, 0.0168, 7, -47.22, 39.27, 0.09132, 3, 4, 50, 33.19, 0.85224, 5, -119.2, -40.99, 0.00694, 7, -38.25, 33.19, 0.14083, 3, 4, 56.47, 29.36, 0.66778, 5, -125.98, -37.76, 0.00088, 7, -31.78, 29.36, 0.33134, 3, 4, 60.74, 29.65, 0.44539, 5, -130.22, -38.43, 0.00028, 7, -27.5, 29.65, 0.55433, 3, 4, 68.12, 35.96, 0.02712, 5, -136.99, -45.39, 1e-05, 7, -20.12, 35.96, 0.97287, 1, 7, -11.87, 40.34, 1, 1, 7, -1.55, 43.45, 1, 1, 7, 18.27, 42.12, 1, 1, 7, 24.94, 38.11, 1, 1, 7, 34.91, 29.38, 1, 1, 7, 43.71, 16.38, 1, 1, 7, 47.44, 5.09, 1, 1, 7, 47.17, -1.09, 1, 1, 7, 42.79, -17.32, 1, 1, 7, 31.18, -30.55, 1, 1, 7, 20.23, -36.05, 1, 1, 7, 5.74, -38.08, 1, 1, 7, -10.26, -36.6, 1, 1, 7, -21.07, -31.48, 1, 2, 4, 58.94, -23.52, 0.20066, 7, -29.31, -23.52, 0.79934, 2, 4, 53.41, -20.83, 0.51644, 7, -34.83, -20.83, 0.48356, 3, 4, 43.02, -23.08, 0.83543, 5, -117.34, 15.69, 0.00161, 7, -45.23, -23.08, 0.16296, 4, 4, 25.77, -30.67, 0.63058, 5, -100.85, 24.81, 0.00795, 7, -62.47, -30.67, 0.07456, 3, -59.86, 28.58, 0.28692, 4, 4, 12.02, -36.94, 0.36236, 5, -87.72, 32.3, 0.00457, 7, -76.23, -36.94, 0.04284, 3, -46.39, 35.44, 0.59023, 5, 4, -3.33, -40.44, 0.17206, 5, -72.75, 37.17, 0.00218, 7, -91.58, -40.44, 0.02034, 8, -170.09, 37.17, 0, 3, -31.21, 39.6, 0.80541, 5, 4, -22.82, -44.43, 0.03576, 5, -53.7, 42.9, 0.0011, 7, -111.07, -44.43, 0.00423, 8, -151.04, 42.9, 0.0001, 3, -11.91, 44.42, 0.95882, 5, 4, -49.98, -43.25, 0.00313, 5, -26.55, 44.19, 0.01153, 7, -138.23, -43.25, 0.00036, 8, -123.88, 44.19, 0.0018, 3, 15.28, 44.42, 0.98318], "hull": 62, "edges": [94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 92, 94, 90, 92], "width": 338, "height": 106}}, "brow_l": {"brow_l": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-18.1, 2.12, 9.57, 16.1, 19.94, -4.43, -7.72, -18.41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 23}}, "brow_r": {"brow_r": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [18.36, -15.38, -18.46, -11.76, -16.5, 8.15, 20.32, 4.52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 37, "height": 20}}, "eyelid_l_l": {"eyelid_l_l": {"type": "mesh", "uvs": [0.9809, 0.20297, 0.92386, 0.41444, 0.77747, 0.73434, 0.58652, 0.92411, 0.36056, 0.99999, 0.13141, 0.897, 0, 0.76687, 0.24917, 0.46866, 0.41943, 0.32497, 0.59606, 0.17586, 0.80929, 0.06506, 0.97135, 0.03795, 0.316, 0.7994, 0.50377, 0.66385, 0.69154, 0.46866, 0.87613, 0.21382], "triangles": [7, 8, 13, 14, 8, 9, 14, 9, 10, 15, 10, 11, 5, 6, 7, 14, 10, 15, 12, 7, 13, 13, 14, 2, 5, 12, 4, 3, 13, 2, 4, 13, 3, 2, 14, 1, 4, 12, 13, 1, 15, 0, 14, 15, 1, 12, 5, 7, 14, 13, 8, 15, 11, 0], "vertices": [2, 29, -27.74, -21.24, 0.94622, 31, -22.58, 6.14, 0.05378, 1, 29, -24.88, -15.65, 1, 1, 29, -17.77, -7.31, 1, 1, 29, -8.78, -2.57, 1, 1, 29, 1.69, -0.97, 1, 2, 29, 12.11, -4.2, 0.81603, 31, 20.7, 8.24, 0.18397, 1, 31, 24.91, 2.66, 1, 1, 31, 11.22, -0.32, 1, 1, 31, 2.5, -0.85, 1, 1, 31, -6.55, -1.41, 1, 1, 31, -16.75, -0.36, 1, 2, 29, -27.5, -25.71, 0.03411, 31, -23.91, 1.86, 0.96589, 2, 29, 3.51, -6.47, 0.98135, 31, 11.86, 9.11, 0.01865, 2, 29, -5.28, -9.75, 0.82132, 31, 2.48, 9.09, 0.17868, 2, 29, -14.14, -14.64, 0.89345, 31, -7.53, 7.58, 0.10655, 2, 29, -22.92, -21.15, 0.74382, 31, -18.03, 4.54, 0.25618], "hull": 12, "edges": [12, 14, 20, 22, 18, 20, 0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 10, 24, 24, 26, 26, 28, 28, 30, 30, 22, 14, 16, 16, 18], "width": 46, "height": 27}}, "eyelid_l_r": {"eyelid_l_r": {"type": "mesh", "uvs": [1, 0.18636, 0.86744, 0.6148, 0.62596, 0.99999, 0.3611, 1, 0.1248, 0.67323, 0, 0.28373, 0.18712, 0.14741, 0.5, 0, 0.79993, 0, 0.27022, 0.64077, 0.57143, 0.5434, 0.8259, 0.42005], "triangles": [4, 5, 6, 9, 6, 7, 10, 7, 8, 11, 8, 0, 10, 8, 11, 4, 6, 9, 1, 11, 0, 3, 10, 2, 3, 9, 10, 2, 11, 1, 2, 10, 11, 4, 9, 3, 9, 7, 10], "vertices": [2, 32, 23.21, 18.52, 0.22286, 33, 21.8, -1.06, 0.77714, 1, 32, 17, 9.64, 1, 1, 32, 5.3, 1.37, 1, 1, 32, -7.92, 0.75, 1, 2, 32, -20.03, 6.71, 0.99429, 33, -21.45, -12.87, 0.00571, 2, 32, -26.64, 14.2, 0.18857, 33, -28.05, -5.38, 0.81143, 1, 33, -18.83, -2.21, 1, 1, 33, -3.35, 1.47, 1, 1, 33, 11.63, 2.18, 1, 2, 32, -12.8, 7.71, 0.97857, 33, -14.21, -11.87, 0.02143, 1, 32, 2.15, 10.37, 1, 1, 32, 14.74, 13.43, 1], "hull": 9, "edges": [10, 12, 12, 14, 14, 16, 16, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 8, 18, 18, 20, 20, 22], "width": 50, "height": 20}}, "eyelid_u_l": {"eyelid_u_l": {"type": "mesh", "uvs": [0.75448, 0.0787, 0.88022, 0.18272, 0.95277, 0.29195, 1, 0.47919, 1, 0.61962, 0.93769, 0.61962, 0.87539, 0.61962, 0.78998, 0.63166, 0.70548, 0.6567, 0.62275, 0.69166, 0.55867, 0.72715, 0.48371, 0.7674, 0.41061, 0.81717, 0.36276, 0.85514, 0.3144, 0.89823, 0.26603, 0.94131, 0.21767, 0.9844, 0.13062, 0.88488, 0.04841, 0.75485, 1e-05, 0.58841, 0, 0.43238, 0.0871, 0.23473, 0.24669, 0.0683, 0.5, 0, 0.59489, 0, 0.28805, 0.61513, 0.59684, 0.40661, 0.79317, 0.34396, 0.15928, 0.38858, 0.46915, 0.17752, 0.66743, 0.14111, 0.18134, 0.77274, 0.22498, 0.69232, 0.35335, 0.54953, 0.44725, 0.47834, 0.52034, 0.43484, 0.66345, 0.38201, 0.72831, 0.36298, 0.87297, 0.31795, 0.05723, 0.51198, 0.10843, 0.44357, 0.22508, 0.32715, 0.31843, 0.25816, 0.38983, 0.21466, 0.56829, 0.15932, 0.77903, 0.16002], "triangles": [25, 33, 11, 33, 34, 10, 34, 35, 9, 5, 6, 3, 7, 27, 6, 8, 36, 7, 9, 26, 8, 10, 34, 9, 11, 33, 10, 12, 25, 11, 12, 13, 32, 14, 31, 13, 14, 15, 31, 16, 17, 15, 35, 26, 9, 5, 3, 4, 30, 24, 0, 44, 23, 24, 44, 24, 30, 1, 45, 0, 30, 0, 45, 29, 22, 23, 29, 23, 44, 43, 22, 29, 42, 22, 43, 41, 21, 22, 38, 45, 1, 38, 1, 2, 42, 41, 22, 27, 45, 38, 37, 30, 45, 37, 45, 27, 36, 44, 30, 36, 30, 37, 28, 21, 41, 26, 44, 36, 29, 44, 26, 40, 20, 21, 35, 29, 26, 43, 29, 35, 28, 40, 21, 34, 43, 35, 42, 43, 34, 41, 42, 34, 38, 2, 3, 39, 20, 40, 33, 41, 34, 28, 41, 33, 19, 20, 39, 25, 28, 33, 40, 28, 25, 39, 40, 25, 3, 27, 38, 6, 27, 3, 7, 37, 27, 36, 37, 7, 32, 39, 25, 19, 39, 32, 18, 19, 32, 31, 18, 32, 17, 18, 31, 15, 17, 31, 26, 36, 8, 32, 25, 12, 13, 31, 32], "vertices": [2, 29, -15.99, -53.08, 0.86761, 35, -23.29, -25.06, 0.13239, 2, 29, -22.91, -47.27, 0.83789, 35, -27.63, -17.12, 0.16211, 2, 29, -26.32, -41.36, 0.82396, 35, -28.65, -10.39, 0.17604, 2, 29, -29.68, -31.57, 0.65102, 35, -28.22, -0.04, 0.34898, 2, 29, -30.55, -24.76, 0.50409, 35, -26.56, 6.62, 0.49591, 2, 29, -26.1, -24.45, 0.41047, 35, -22.29, 5.29, 0.58953, 2, 29, -21.64, -24.14, 0.36092, 35, -18.03, 3.95, 0.63908, 2, 29, -16.78, -23.74, 0.3381, 35, -13.36, 2.56, 0.6619, 2, 29, -11.88, -22.6, 0.31722, 35, -8.38, 1.84, 0.68278, 2, 29, -7.06, -20.93, 0.31286, 35, -3.28, 1.64, 0.68714, 2, 29, -3.34, -19.21, 0.30569, 35, 0.81, 1.89, 0.69431, 2, 29, 1.01, -17.28, 0.31196, 35, 5.56, 2.11, 0.68804, 2, 29, 5.25, -14.85, 0.32676, 35, 10.4, 2.83, 0.67324, 2, 29, 8.49, -12.41, 0.32976, 35, 14.3, 3.93, 0.67024, 2, 29, 11.74, -9.72, 0.35867, 35, 18.31, 5.25, 0.64133, 2, 29, 14.99, -7.03, 0.39925, 35, 22.31, 6.57, 0.60075, 2, 29, 18.24, -4.35, 0.47508, 35, 26.32, 7.89, 0.52492, 2, 29, 23.26, -10.84, 0.59512, 35, 28.63, 0.01, 0.40488, 2, 29, 26.79, -18.56, 0.75044, 35, 29.11, -8.46, 0.24956, 2, 29, 28.14, -27.95, 0.84958, 35, 26.95, -17.7, 0.15042, 2, 29, 27.78, -36.21, 0.90966, 35, 23.62, -25.26, 0.09034, 2, 29, 22.37, -46.46, 0.93979, 35, 14.85, -32.84, 0.06021, 2, 29, 12.9, -54.88, 0.95603, 35, 2.97, -37.24, 0.04397, 2, 29, -1.68, -57.88, 0.93485, 35, -11.71, -34.73, 0.06515, 2, 29, -7.08, -57.64, 0.92536, 35, -16.65, -32.55, 0.07464, 2, 29, 13.76, -24.36, 0.58815, 35, 14.87, -9.12, 0.41185, 2, 29, -6.3, -36.12, 0.58519, 35, -8.1, -12.78, 0.41481, 2, 29, -18.62, -39.73, 0.67553, 35, -20.89, -11.67, 0.32447, 2, 29, 19.22, -37.75, 0.82916, 35, 15.08, -23.59, 0.17084, 2, 29, 0.48, -48.55, 0.84416, 35, -6.3, -26.83, 0.15584, 2, 29, -11.4, -50.14, 0.86697, 35, -17.94, -23.99, 0.13303, 2, 29, 20.19, -16.49, 0.58507, 35, 23.72, -4.13, 0.41493, 2, 29, 17.39, -20.63, 0.59346, 35, 19.61, -6.97, 0.40654, 2, 29, 9.29, -28.12, 0.57765, 35, 9.34, -11, 0.42235, 2, 29, 3.12, -32.16, 0.57717, 35, 2.11, -12.52, 0.42283, 2, 29, -1.47, -34.53, 0.57991, 35, -3.02, -13.05, 0.42009, 2, 29, -10.52, -37.55, 0.60394, 35, -12.55, -12.59, 0.39606, 2, 29, -14.57, -38.64, 0.63016, 35, -16.72, -12.13, 0.36984, 2, 29, -22.47, -40.55, 0.74696, 35, -24.77, -11.03, 0.25304, 2, 29, 24.89, -31.75, 0.83665, 35, 22.55, -20.05, 0.16335, 2, 29, 21.98, -35.14, 0.84433, 35, 18.61, -22.16, 0.15567, 2, 29, 15.1, -41.01, 0.82496, 35, 10.05, -25.12, 0.17504, 2, 29, 9.47, -44.52, 0.83334, 35, 3.54, -26.35, 0.16666, 2, 29, 5.2, -46.72, 0.83589, 35, -1.24, -26.84, 0.16411, 2, 29, -5.46, -49.34, 0.83271, 35, -12.12, -25.41, 0.16729, 2, 29, -17.43, -48.78, 0.86707, 35, -23.07, -20.53, 0.13293], "hull": 25, "edges": [40, 42, 42, 44, 44, 46, 46, 48, 48, 0, 0, 2, 2, 4, 8, 6, 4, 6, 38, 40, 38, 36, 36, 34, 34, 32, 16, 18, 18, 20, 12, 14, 14, 16, 8, 10, 10, 12, 20, 22, 22, 24, 28, 30, 30, 32, 24, 26, 26, 28, 34, 62, 50, 64, 64, 62, 50, 66, 68, 66, 52, 70, 70, 68, 52, 72, 54, 74, 74, 72, 4, 76, 76, 54, 38, 78, 56, 80, 80, 78, 56, 82, 84, 82, 58, 86, 86, 84, 58, 88, 88, 60], "width": 57, "height": 53}}, "eyelid_u_r": {"eyelid_u_r": {"type": "mesh", "uvs": [0.73132, 0.06493, 0.85025, 0.15925, 0.93638, 0.27819, 1, 0.40122, 1, 0.5188, 0.97876, 0.65044, 0.93542, 0.75157, 0.88137, 0.72268, 0.78986, 0.69699, 0.68391, 0.69057, 0.57635, 0.69378, 0.48966, 0.6986, 0.40297, 0.70341, 0.30424, 0.72107, 0.20551, 0.73873, 0.07709, 0.77084, 0.03214, 0.68415, 0, 0.5798, 0, 0.40942, 0.05463, 0.26588, 0.14896, 0.14285, 0.24739, 0.06903, 0.42374, 0.0082, 0.57548, 1e-05, 0.14559, 0.51398, 0.25315, 0.48669, 0.38318, 0.46743, 0.51161, 0.46101, 0.64164, 0.4578, 0.74599, 0.46261, 0.8696, 0.47867], "triangles": [9, 28, 29, 9, 29, 8, 11, 26, 27, 11, 27, 10, 12, 26, 11, 25, 26, 12, 15, 16, 14, 6, 7, 5, 14, 16, 24, 13, 25, 12, 14, 24, 25, 25, 13, 14, 10, 27, 28, 10, 28, 9, 8, 29, 30, 7, 8, 30, 5, 7, 30, 27, 26, 22, 27, 22, 23, 26, 21, 22, 16, 17, 24, 5, 30, 4, 0, 27, 23, 28, 0, 1, 29, 28, 1, 2, 29, 1, 28, 27, 0, 26, 20, 21, 26, 19, 20, 18, 19, 24, 2, 30, 29, 3, 30, 2, 26, 25, 19, 25, 24, 19, 30, 3, 4, 17, 18, 24], "vertices": [2, 32, 9.61, 55.53, 0.92766, 36, 10.17, 34.38, 0.07234, 2, 32, 16.68, 50.34, 0.88405, 36, 17.24, 29.19, 0.11595, 2, 32, 21.98, 43.58, 0.83723, 36, 22.53, 22.43, 0.16277, 2, 32, 25.91, 36.56, 0.80015, 36, 26.46, 15.41, 0.19985, 2, 32, 26.45, 28.45, 0.65569, 36, 26.9, 7.3, 0.34431, 2, 32, 25.57, 20.26, 0.45321, 36, 26.01, -0.88, 0.54679, 2, 32, 23.22, 13.81, 0.32341, 36, 23.64, -7.3, 0.67659, 2, 32, 20.24, 15.18, 0.22761, 36, 20.66, -5.94, 0.77239, 2, 32, 15.1, 16.62, 0.14103, 36, 15.54, -4.5, 0.85897, 2, 32, 9.09, 17.11, 0.06823, 36, 9.54, -4.01, 0.93177, 2, 32, 2.94, 17.13, 0.0247, 36, 3.42, -3.99, 0.9753, 2, 32, -2.03, 17.05, 0.02664, 36, -1.53, -4.07, 0.97336, 2, 32, -7.01, 16.98, 0.04901, 36, -6.49, -4.14, 0.95099, 2, 32, -12.72, 16.19, 0.09029, 36, -12.18, -4.93, 0.90971, 2, 32, -18.44, 15.35, 0.17048, 36, -17.88, -5.76, 0.82952, 2, 32, -25.88, 13.6, 0.29219, 36, -25.31, -7.51, 0.70781, 2, 32, -28.51, 18.42, 0.45173, 36, -27.95, -2.71, 0.54827, 2, 32, -30.58, 24.14, 0.58253, 36, -30.04, 3, 0.41747, 2, 32, -31.12, 34.49, 0.80421, 36, -30.52, 13.34, 0.19579, 2, 32, -28.42, 42.69, 0.89831, 36, -27.83, 21.54, 0.10169, 2, 32, -23.37, 49.86, 0.93167, 36, -22.79, 28.71, 0.06833, 2, 32, -17.97, 54.27, 0.96, 36, -17.39, 33.12, 0.04, 2, 32, -8.19, 58.13, 0.96, 36, -7.62, 36.98, 0.04, 2, 32, 0.44, 58.94, 0.95808, 36, 1.01, 37.79, 0.04192, 2, 32, -22.06, 28.12, 0.52182, 36, -21.52, 6.97, 0.47819, 2, 32, -15.92, 29.68, 0.469, 36, -15.39, 8.53, 0.531, 2, 32, -8.49, 30.79, 0.46738, 36, -7.97, 9.63, 0.53262, 2, 32, -1.15, 31.16, 0.46742, 36, -0.65, 10, 0.53258, 2, 32, 6.27, 31.35, 0.46872, 36, 6.75, 10.19, 0.53128, 2, 32, 12.23, 31.09, 0.48774, 36, 12.7, 9.92, 0.51226, 2, 32, 19.18, 30.34, 0.53916, 36, 19.64, 9.18, 0.46084], "hull": 24, "edges": [36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 0, 0, 2, 2, 4, 4, 6, 34, 36, 34, 32, 32, 30, 30, 28, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 24, 26, 26, 28, 20, 22, 22, 24, 34, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 6, 8, 60, 8, 8, 10], "width": 57, "height": 57}}, "eye_l": {"eye_l": {"type": "mesh", "uvs": [0.76971, 0.07871, 0.90323, 0.21223, 0.9891, 0.38934, 0.99183, 0.59098, 0.93047, 0.76537, 0.80786, 0.89616, 0.69614, 0.95611, 0.56535, 0.99183, 0.42365, 0.9891, 0.25744, 0.93704, 0.14027, 0.83077, 0.04218, 0.70815, 0.01362, 0.57463, 0.00545, 0.43567, 0.0558, 0.26945, 0.14845, 0.14411, 0.26834, 0.0666, 0.40458, 0.01635, 0.57897, 0.0109, 0.5163, 0.52286], "triangles": [6, 7, 19, 19, 7, 8, 5, 6, 19, 9, 19, 8, 9, 10, 19, 5, 19, 4, 10, 11, 19, 4, 19, 3, 11, 12, 19, 19, 2, 3, 12, 13, 19, 13, 14, 19, 14, 15, 19, 15, 16, 19, 16, 17, 19, 19, 1, 2, 19, 0, 1, 19, 18, 0, 19, 17, 18], "vertices": [1, 29, -18.57, -55.19, 1, 1, 29, -26.61, -46.42, 1, 1, 29, -31.54, -35.04, 1, 1, 29, -31.16, -22.34, 1, 1, 29, -26.82, -11.53, 1, 1, 29, -18.75, -3.63, 1, 1, 29, -11.55, -0.16, 1, 1, 29, -3.22, 1.73, 1, 1, 29, 5.69, 1.17, 1, 1, 29, 16.01, -2.56, 1, 1, 29, 23.09, -9.56, 1, 1, 29, 28.93, -17.55, 1, 1, 29, 30.37, -26.03, 1, 1, 29, 30.5, -34.8, 1, 1, 29, 26.88, -45.12, 1, 1, 29, 20.71, -52.76, 1, 1, 29, 12.95, -57.31, 1, 1, 29, 4.24, -60.1, 1, 1, 29, -6.75, -59.97, 1, 2, 29, -1.41, -27.92, 0.75, 30, 5.6, 0.65, 0.25], "hull": 19, "edges": [26, 28, 28, 30, 34, 36, 36, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 24, 26, 22, 24, 30, 32, 32, 34], "width": 63, "height": 63}}, "eye_r": {"eye_r": {"type": "mesh", "uvs": [0.77071, 0.07377, 0.87993, 0.17063, 0.97061, 0.33344, 0.99588, 0.4777, 0.97733, 0.64256, 0.91084, 0.7827, 0.78925, 0.90841, 0.59554, 0.98764, 0.43067, 0.99382, 0.25138, 0.92695, 0.08033, 0.76827, 0.00412, 0.57868, 0.0103, 0.42205, 0.0659, 0.25513, 0.21016, 0.09026, 0.42243, 0.0103, 0.57493, 0.0103, 0.48631, 0.50449], "triangles": [17, 15, 16, 17, 16, 0, 17, 0, 1, 17, 1, 2, 17, 2, 3, 14, 15, 17, 13, 14, 17, 12, 13, 17, 11, 12, 17, 4, 17, 3, 10, 11, 17, 5, 17, 4, 6, 17, 5, 9, 10, 17, 17, 8, 9, 6, 7, 17, 7, 8, 17], "vertices": [1, 32, 12.92, 57.15, 1, 1, 32, 20.09, 51.38, 1, 1, 32, 26.28, 41.41, 1, 1, 32, 28.3, 32.4, 1, 1, 32, 27.63, 21.97, 1, 1, 32, 23.86, 12.96, 1, 1, 32, 16.59, 4.68, 1, 1, 32, 4.63, -0.88, 1, 1, 32, -5.72, -1.76, 1, 1, 32, -17.21, 1.91, 1, 1, 32, -28.45, 11.38, 1, 1, 32, -33.81, 23.09, 1, 1, 32, -33.89, 32.96, 1, 1, 32, -30.89, 43.63, 1, 1, 32, -22.3, 54.44, 1, 1, 32, -9.18, 60.1, 1, 1, 32, 0.41, 60.56, 1, 2, 34, -7.23, -0.3, 0.31429, 32, -3.69, 29.2, 0.68571], "hull": 17, "edges": [24, 26, 26, 28, 28, 30, 30, 32, 32, 0, 0, 2, 2, 4, 4, 6, 22, 24, 22, 20, 20, 18, 18, 16, 14, 16, 14, 12, 12, 10, 6, 8, 10, 8], "width": 63, "height": 63}}, "leg_big": {"leg_big": {"type": "mesh", "uvs": [0.85502, 0.06928, 0.96614, 0.13856, 0.94357, 0.30058, 0.90298, 0.39652, 0.8624, 0.49246, 0.83419, 0.54736, 0.80597, 0.60226, 0.77775, 0.65715, 0.74954, 0.71205, 0.73825, 0.74882, 0.72696, 0.7856, 0.70045, 0.84347, 0.67393, 0.90135, 0.63103, 0.99787, 0.41094, 1, 0.32854, 0.89922, 0.30625, 0.84187, 0.28396, 0.78453, 0.26844, 0.74509, 0.25292, 0.70565, 0.2278, 0.65955, 0.20268, 0.61345, 0.16854, 0.55082, 0.13441, 0.48819, 0.08696, 0.39119, 0.0395, 0.29419, 0.04515, 0.13856, 0.11235, 0.06928, 0.25292, 0.02345, 0.4843, 0, 0.73261, 0.02772], "triangles": [2, 26, 27, 27, 1, 2, 27, 30, 0, 27, 28, 30, 28, 29, 30, 1, 27, 0, 13, 14, 12, 12, 14, 15, 12, 15, 11, 15, 16, 11, 11, 16, 17, 10, 11, 17, 10, 17, 9, 17, 18, 9, 9, 18, 8, 18, 19, 8, 8, 19, 7, 19, 20, 7, 20, 21, 7, 7, 21, 6, 21, 22, 6, 6, 22, 5, 5, 22, 4, 4, 22, 23, 4, 23, 3, 23, 24, 2, 3, 23, 2, 2, 24, 25, 26, 2, 25], "vertices": [3, 10, -20.55, 12.23, 0.24878, 11, -48.05, 12.23, 0.0005, 9, -20.55, 12.23, 0.75071, 3, 10, -14.32, 16.01, 0.43833, 11, -41.82, 16.01, 0.00401, 9, -14.32, 16.01, 0.55765, 3, 10, 0.27, 15.24, 0.63012, 11, -27.23, 15.24, 0.01785, 9, 0.27, 15.24, 0.35203, 4, 10, 8.9, 13.86, 0.75903, 11, -18.6, 13.86, 0.05725, 12, -35.1, 13.86, 0.00019, 9, 8.9, 13.86, 0.18353, 4, 10, 17.54, 12.48, 0.78374, 11, -9.96, 12.48, 0.139, 12, -26.47, 12.48, 0.00117, 9, 17.54, 12.48, 0.07609, 4, 10, 22.48, 11.52, 0.69861, 11, -5.02, 11.52, 0.27104, 12, -21.52, 11.52, 0.00604, 9, 22.48, 11.52, 0.02431, 4, 10, 27.42, 10.56, 0.53873, 11, -0.08, 10.56, 0.43398, 12, -16.58, 10.56, 0.02279, 9, 27.42, 10.56, 0.0045, 5, 10, 32.36, 9.6, 0.35864, 11, 4.86, 9.6, 0.56233, 12, -11.64, 9.6, 0.07643, 13, -22.64, 9.6, 0.0019, 9, 32.36, 9.6, 0.00071, 4, 10, 37.3, 8.64, 0.20038, 11, 9.8, 8.64, 0.62276, 12, -6.7, 8.64, 0.16485, 13, -17.7, 8.64, 0.012, 4, 10, 40.61, 8.26, 0.08915, 11, 13.11, 8.26, 0.5872, 12, -3.39, 8.26, 0.27683, 13, -14.39, 8.26, 0.04682, 4, 10, 43.92, 7.88, 0.02589, 11, 16.42, 7.88, 0.47075, 12, -0.08, 7.88, 0.37483, 13, -11.08, 7.88, 0.12852, 4, 10, 49.13, 6.97, 0.00641, 11, 21.63, 6.97, 0.30419, 12, 5.13, 6.97, 0.39471, 13, -5.88, 6.97, 0.29469, 4, 10, 54.34, 6.07, 0.00108, 11, 26.83, 6.07, 0.16245, 12, 10.33, 6.07, 0.36452, 13, -0.67, 6.07, 0.47194, 4, 10, 63.02, 4.61, 0.00017, 11, 35.52, 4.61, 0.08411, 12, 19.02, 4.61, 0.3313, 13, 8.02, 4.61, 0.58442, 4, 10, 63.21, -2.87, 0.00015, 11, 35.71, -2.87, 0.08583, 12, 19.21, -2.87, 0.33997, 13, 8.21, -2.87, 0.57405, 4, 10, 54.14, -5.67, 0.00097, 11, 26.64, -5.67, 0.16744, 12, 10.14, -5.67, 0.38461, 13, -0.86, -5.67, 0.44697, 4, 10, 48.98, -6.43, 0.0057, 11, 21.48, -6.43, 0.31143, 12, 4.98, -6.43, 0.41175, 13, -6.02, -6.43, 0.27112, 4, 10, 43.82, -7.19, 0.02336, 11, 16.32, -7.19, 0.47918, 12, -0.18, -7.19, 0.37769, 13, -11.18, -7.19, 0.11977, 4, 10, 40.27, -7.71, 0.07891, 11, 12.77, -7.71, 0.61063, 12, -3.73, -7.71, 0.26911, 13, -14.73, -7.71, 0.04135, 4, 10, 36.72, -8.24, 0.18275, 11, 9.22, -8.24, 0.65528, 12, -7.28, -8.24, 0.15216, 13, -18.28, -8.24, 0.00981, 5, 10, 32.57, -9.1, 0.34003, 11, 5.07, -9.1, 0.59177, 12, -11.43, -9.1, 0.06615, 13, -22.43, -9.1, 0.00146, 9, 32.57, -9.1, 0.00059, 4, 10, 28.43, -9.95, 0.52817, 11, 0.92, -9.95, 0.44843, 12, -15.58, -9.95, 0.01947, 9, 28.43, -9.95, 0.00393, 4, 10, 22.79, -11.11, 0.68494, 11, -4.71, -11.11, 0.28868, 12, -21.21, -11.11, 0.00431, 9, 22.79, -11.11, 0.02207, 4, 10, 17.15, -12.27, 0.77339, 11, -10.35, -12.27, 0.15301, 12, -26.85, -12.27, 0.00057, 9, 17.15, -12.27, 0.07303, 4, 10, 8.42, -13.88, 0.75325, 11, -19.08, -13.88, 0.06399, 12, -35.58, -13.88, 7e-05, 9, 8.42, -13.88, 0.18269, 3, 10, -0.31, -15.5, 0.62346, 11, -27.81, -15.5, 0.01959, 9, -0.31, -15.5, 0.35696, 3, 10, -14.32, -15.31, 0.4269, 11, -41.82, -15.31, 0.00385, 9, -14.32, -15.31, 0.56925, 3, 10, -20.55, -13.02, 0.23489, 11, -48.05, -13.02, 0.00028, 9, -20.55, -13.02, 0.76482, 3, 10, -24.67, -8.24, 0.10434, 11, -52.18, -8.24, 5e-05, 9, -24.67, -8.24, 0.89561, 2, 10, -26.79, -0.37, 0.06302, 9, -26.79, -0.37, 0.93698, 3, 10, -24.29, 8.07, 0.11401, 11, -51.79, 8.07, 8e-05, 9, -24.29, 8.07, 0.88591], "hull": 31, "edges": [50, 52, 30, 28, 26, 28, 26, 24, 4, 2, 2, 0, 0, 60, 52, 54, 54, 56, 56, 58, 58, 60, 42, 12, 34, 20, 42, 44, 44, 46, 46, 48, 48, 50, 34, 36, 36, 38, 38, 40, 40, 42, 30, 32, 32, 34, 20, 22, 22, 24, 16, 18, 18, 20, 12, 14, 14, 16, 8, 10, 10, 12, 4, 6, 6, 8], "width": 34, "height": 90}}, "leg_big2": {"leg_big": {"type": "mesh", "uvs": [0.85502, 0.06928, 0.96614, 0.13856, 0.94357, 0.30058, 0.90298, 0.39652, 0.8624, 0.49246, 0.83419, 0.54736, 0.80597, 0.60226, 0.77775, 0.65715, 0.74954, 0.71205, 0.73825, 0.74882, 0.72696, 0.7856, 0.70045, 0.84347, 0.67393, 0.90135, 0.63103, 0.99787, 0.41094, 1, 0.32854, 0.89922, 0.30625, 0.84187, 0.28396, 0.78453, 0.26844, 0.74509, 0.25292, 0.70565, 0.2278, 0.65955, 0.20268, 0.61345, 0.16854, 0.55082, 0.13441, 0.48819, 0.08696, 0.39119, 0.0395, 0.29419, 0.04515, 0.13856, 0.11235, 0.06928, 0.25292, 0.02345, 0.4843, 0, 0.73261, 0.02772], "triangles": [13, 14, 12, 12, 14, 15, 12, 15, 11, 15, 16, 11, 11, 16, 17, 10, 11, 17, 10, 17, 9, 17, 18, 9, 9, 18, 8, 18, 19, 8, 8, 19, 7, 19, 20, 7, 20, 21, 7, 7, 21, 6, 21, 22, 6, 6, 22, 5, 5, 22, 4, 4, 22, 23, 4, 23, 3, 23, 24, 2, 3, 23, 2, 2, 24, 25, 26, 2, 25, 2, 26, 27, 27, 1, 2, 27, 30, 0, 27, 28, 30, 28, 29, 30, 1, 27, 0], "vertices": [3, 3, -10.06, -24.82, 0.75071, 10, -20.55, 12.23, 0.24878, 11, -48.05, 12.23, 0.0005, 3, 3, -6.28, -31.05, 0.55765, 10, -14.32, 16.01, 0.43833, 11, -41.82, 16.01, 0.00401, 3, 3, -7.04, -45.63, 0.35203, 10, 0.27, 15.24, 0.63012, 11, -27.23, 15.24, 0.01785, 4, 3, -8.42, -54.27, 0.18353, 10, 8.9, 13.86, 0.75903, 11, -18.6, 13.86, 0.05725, 12, -35.1, 13.86, 0.00019, 4, 3, -9.8, -62.9, 0.07609, 10, 17.54, 12.48, 0.78374, 11, -9.96, 12.48, 0.139, 12, -26.47, 12.48, 0.00117, 4, 3, -10.76, -67.84, 0.02431, 10, 22.48, 11.52, 0.69861, 11, -5.02, 11.52, 0.27104, 12, -21.52, 11.52, 0.00604, 4, 3, -11.72, -72.78, 0.0045, 10, 27.42, 10.56, 0.53873, 11, -0.08, 10.56, 0.43398, 12, -16.58, 10.56, 0.02279, 5, 3, -12.68, -77.72, 0.00071, 10, 32.36, 9.6, 0.35864, 11, 4.86, 9.6, 0.56233, 12, -11.64, 9.6, 0.07643, 13, -22.64, 9.6, 0.0019, 4, 10, 37.3, 8.64, 0.20038, 11, 9.8, 8.64, 0.62276, 12, -6.7, 8.64, 0.16485, 13, -17.7, 8.64, 0.012, 4, 10, 40.61, 8.26, 0.08915, 11, 13.11, 8.26, 0.5872, 12, -3.39, 8.26, 0.27683, 13, -14.39, 8.26, 0.04682, 4, 10, 43.92, 7.88, 0.02589, 11, 16.42, 7.88, 0.47075, 12, -0.08, 7.88, 0.37483, 13, -11.08, 7.88, 0.12852, 4, 10, 49.13, 6.97, 0.00641, 11, 21.63, 6.97, 0.30419, 12, 5.13, 6.97, 0.39471, 13, -5.88, 6.97, 0.29469, 4, 10, 54.34, 6.07, 0.00108, 11, 26.83, 6.07, 0.16245, 12, 10.33, 6.07, 0.36452, 13, -0.67, 6.07, 0.47194, 4, 10, 63.02, 4.61, 0.00017, 11, 35.52, 4.61, 0.08411, 12, 19.02, 4.61, 0.3313, 13, 8.02, 4.61, 0.58442, 4, 10, 63.21, -2.87, 0.00015, 11, 35.71, -2.87, 0.08583, 12, 19.21, -2.87, 0.33997, 13, 8.21, -2.87, 0.57405, 4, 10, 54.14, -5.67, 0.00097, 11, 26.64, -5.67, 0.16744, 12, 10.14, -5.67, 0.38461, 13, -0.86, -5.67, 0.44697, 4, 10, 48.98, -6.43, 0.0057, 11, 21.48, -6.43, 0.31143, 12, 4.98, -6.43, 0.41175, 13, -6.02, -6.43, 0.27112, 4, 10, 43.82, -7.19, 0.02336, 11, 16.32, -7.19, 0.47918, 12, -0.18, -7.19, 0.37769, 13, -11.18, -7.19, 0.11977, 4, 10, 40.27, -7.71, 0.07891, 11, 12.77, -7.71, 0.61063, 12, -3.73, -7.71, 0.26911, 13, -14.73, -7.71, 0.04135, 4, 10, 36.72, -8.24, 0.18275, 11, 9.22, -8.24, 0.65528, 12, -7.28, -8.24, 0.15216, 13, -18.28, -8.24, 0.00981, 5, 3, -31.38, -77.94, 0.00059, 10, 32.57, -9.1, 0.34003, 11, 5.07, -9.1, 0.59177, 12, -11.43, -9.1, 0.06615, 13, -22.43, -9.1, 0.00146, 4, 3, -32.23, -73.79, 0.00393, 10, 28.43, -9.95, 0.52817, 11, 0.92, -9.95, 0.44843, 12, -15.58, -9.95, 0.01947, 4, 3, -33.4, -68.15, 0.02207, 10, 22.79, -11.11, 0.68494, 11, -4.71, -11.11, 0.28868, 12, -21.21, -11.11, 0.00431, 4, 3, -34.56, -62.52, 0.07303, 10, 17.15, -12.27, 0.77339, 11, -10.35, -12.27, 0.15301, 12, -26.85, -12.27, 0.00057, 4, 3, -36.17, -53.79, 0.18269, 10, 8.42, -13.88, 0.75325, 11, -19.08, -13.88, 0.06399, 12, -35.58, -13.88, 7e-05, 3, 3, -37.78, -45.06, 0.35696, 10, -0.31, -15.5, 0.62346, 11, -27.81, -15.5, 0.01959, 3, 3, -37.59, -31.05, 0.56925, 10, -14.32, -15.31, 0.4269, 11, -41.82, -15.31, 0.00385, 3, 3, -35.31, -24.82, 0.76482, 10, -20.55, -13.02, 0.23489, 11, -48.05, -13.02, 0.00028, 3, 3, -30.53, -20.69, 0.89561, 10, -24.67, -8.24, 0.10434, 11, -52.18, -8.24, 5e-05, 2, 3, -22.66, -18.58, 0.93698, 10, -26.79, -0.37, 0.06302, 3, 3, -14.22, -21.08, 0.88591, 10, -24.29, 8.07, 0.11401, 11, -51.79, 8.07, 8e-05], "hull": 31, "edges": [50, 52, 30, 28, 26, 28, 26, 24, 4, 2, 2, 0, 0, 60, 52, 54, 54, 56, 56, 58, 58, 60, 42, 12, 34, 20, 42, 44, 44, 46, 46, 48, 48, 50, 34, 36, 36, 38, 38, 40, 40, 42, 30, 32, 32, 34, 20, 22, 22, 24, 16, 18, 18, 20, 12, 14, 14, 16, 8, 10, 10, 12, 4, 6, 6, 8], "width": 34, "height": 90}}, "leg_big3": {"leg_big": {"type": "mesh", "uvs": [0.85502, 0.06928, 0.96614, 0.13856, 0.94357, 0.30058, 0.90298, 0.39652, 0.8624, 0.49246, 0.83419, 0.54736, 0.80597, 0.60226, 0.77775, 0.65715, 0.74954, 0.71205, 0.73825, 0.74882, 0.72696, 0.7856, 0.70045, 0.84347, 0.67393, 0.90135, 0.63103, 0.99787, 0.41094, 1, 0.32854, 0.89922, 0.30625, 0.84187, 0.28396, 0.78453, 0.26844, 0.74509, 0.25292, 0.70565, 0.2278, 0.65955, 0.20268, 0.61345, 0.16854, 0.55082, 0.13441, 0.48819, 0.08696, 0.39119, 0.0395, 0.29419, 0.04515, 0.13856, 0.11235, 0.06928, 0.25292, 0.02345, 0.4843, 0, 0.73261, 0.02772], "triangles": [2, 26, 27, 27, 1, 2, 27, 30, 0, 27, 28, 30, 28, 29, 30, 1, 27, 0, 13, 14, 12, 12, 14, 15, 12, 15, 11, 15, 16, 11, 11, 16, 17, 10, 11, 17, 10, 17, 9, 17, 18, 9, 9, 18, 8, 18, 19, 8, 8, 19, 7, 19, 20, 7, 20, 21, 7, 7, 21, 6, 21, 22, 6, 6, 22, 5, 5, 22, 4, 4, 22, 23, 4, 23, 3, 23, 24, 2, 3, 23, 2, 2, 24, 25, 26, 2, 25], "vertices": [3, 20, -20.55, 12.23, 0.24878, 21, -48.05, 12.23, 0.0005, 19, -20.55, 12.23, 0.75071, 3, 20, -14.32, 16.01, 0.43833, 21, -41.82, 16.01, 0.00401, 19, -14.32, 16.01, 0.55765, 3, 20, 0.27, 15.24, 0.63012, 21, -27.23, 15.24, 0.01785, 19, 0.27, 15.24, 0.35203, 4, 20, 8.9, 13.86, 0.75903, 21, -18.6, 13.86, 0.05725, 22, -35.1, 13.86, 0.00019, 19, 8.9, 13.86, 0.18353, 4, 20, 17.54, 12.48, 0.78374, 21, -9.96, 12.48, 0.139, 22, -26.47, 12.48, 0.00117, 19, 17.54, 12.48, 0.07609, 4, 20, 22.48, 11.52, 0.69861, 21, -5.02, 11.52, 0.27104, 22, -21.52, 11.52, 0.00604, 19, 22.48, 11.52, 0.02431, 4, 20, 27.42, 10.56, 0.53873, 21, -0.08, 10.56, 0.43398, 22, -16.58, 10.56, 0.02279, 19, 27.42, 10.56, 0.0045, 5, 20, 32.36, 9.6, 0.35864, 21, 4.86, 9.6, 0.56233, 22, -11.64, 9.6, 0.07643, 23, -22.64, 9.6, 0.0019, 19, 32.36, 9.6, 0.00071, 4, 20, 37.3, 8.64, 0.20038, 21, 9.8, 8.64, 0.62276, 22, -6.7, 8.64, 0.16485, 23, -17.7, 8.64, 0.012, 4, 20, 40.61, 8.26, 0.08915, 21, 13.11, 8.26, 0.5872, 22, -3.39, 8.26, 0.27683, 23, -14.39, 8.26, 0.04682, 4, 20, 43.92, 7.88, 0.02589, 21, 16.42, 7.88, 0.47075, 22, -0.08, 7.88, 0.37483, 23, -11.08, 7.88, 0.12852, 4, 20, 49.13, 6.97, 0.00641, 21, 21.63, 6.97, 0.30419, 22, 5.13, 6.97, 0.39471, 23, -5.88, 6.97, 0.29469, 4, 20, 54.34, 6.07, 0.00108, 21, 26.83, 6.07, 0.16245, 22, 10.33, 6.07, 0.36452, 23, -0.67, 6.07, 0.47194, 4, 20, 63.02, 4.61, 0.00017, 21, 35.52, 4.61, 0.08411, 22, 19.02, 4.61, 0.3313, 23, 8.02, 4.61, 0.58442, 4, 20, 63.21, -2.87, 0.00015, 21, 35.71, -2.87, 0.08583, 22, 19.21, -2.87, 0.33997, 23, 8.21, -2.87, 0.57405, 4, 20, 54.14, -5.67, 0.00097, 21, 26.64, -5.67, 0.16744, 22, 10.14, -5.67, 0.38461, 23, -0.86, -5.67, 0.44697, 4, 20, 48.98, -6.43, 0.0057, 21, 21.48, -6.43, 0.31143, 22, 4.98, -6.43, 0.41175, 23, -6.02, -6.43, 0.27112, 4, 20, 43.82, -7.19, 0.02336, 21, 16.32, -7.19, 0.47918, 22, -0.18, -7.19, 0.37769, 23, -11.18, -7.19, 0.11977, 4, 20, 40.27, -7.71, 0.07891, 21, 12.77, -7.71, 0.61063, 22, -3.73, -7.71, 0.26911, 23, -14.73, -7.71, 0.04135, 4, 20, 36.72, -8.24, 0.18275, 21, 9.22, -8.24, 0.65528, 22, -7.28, -8.24, 0.15216, 23, -18.28, -8.24, 0.00981, 5, 20, 32.57, -9.1, 0.34003, 21, 5.07, -9.1, 0.59177, 22, -11.43, -9.1, 0.06615, 23, -22.43, -9.1, 0.00146, 19, 32.57, -9.1, 0.00059, 4, 20, 28.43, -9.95, 0.52817, 21, 0.92, -9.95, 0.44843, 22, -15.58, -9.95, 0.01947, 19, 28.43, -9.95, 0.00393, 4, 20, 22.79, -11.11, 0.68494, 21, -4.71, -11.11, 0.28868, 22, -21.21, -11.11, 0.00431, 19, 22.79, -11.11, 0.02207, 4, 20, 17.15, -12.27, 0.77339, 21, -10.35, -12.27, 0.15301, 22, -26.85, -12.27, 0.00057, 19, 17.15, -12.27, 0.07303, 4, 20, 8.42, -13.88, 0.75325, 21, -19.08, -13.88, 0.06399, 22, -35.58, -13.88, 7e-05, 19, 8.42, -13.88, 0.18269, 3, 20, -0.31, -15.5, 0.62346, 21, -27.81, -15.5, 0.01959, 19, -0.31, -15.5, 0.35696, 3, 20, -14.32, -15.31, 0.4269, 21, -41.82, -15.31, 0.00385, 19, -14.32, -15.31, 0.56925, 3, 20, -20.55, -13.02, 0.23489, 21, -48.05, -13.02, 0.00028, 19, -20.55, -13.02, 0.76482, 3, 20, -24.67, -8.24, 0.10434, 21, -52.18, -8.24, 5e-05, 19, -24.67, -8.24, 0.89561, 2, 20, -26.79, -0.37, 0.06302, 19, -26.79, -0.37, 0.93698, 3, 20, -24.29, 8.07, 0.11401, 21, -51.79, 8.07, 8e-05, 19, -24.29, 8.07, 0.88591], "hull": 31, "edges": [50, 52, 30, 28, 26, 28, 26, 24, 4, 2, 2, 0, 0, 60, 52, 54, 54, 56, 56, 58, 58, 60, 42, 12, 34, 20, 42, 44, 44, 46, 46, 48, 48, 50, 34, 36, 36, 38, 38, 40, 40, 42, 30, 32, 32, 34, 20, 22, 22, 24, 16, 18, 18, 20, 12, 14, 14, 16, 8, 10, 10, 12, 4, 6, 6, 8], "width": 34, "height": 90}}, "leg_big_outline": {"leg_big_outline": {"type": "mesh", "uvs": [0.84826, 0.06864, 0.99477, 0.17719, 0.97907, 0.31238, 0.76454, 0.86457, 0.71221, 0.9448, 0.5971, 1, 0.4035, 1, 0.28315, 0.93064, 0.2465, 0.85986, 0.1471, 0.60666, 0, 0.2173, 0.05815, 0.12763, 0.18896, 0.04504, 0.4035, 0, 0.70175, 0, 0.05815, 0.35889, 0.21513, 0.75366, 0.86395, 0.60499, 0.80116, 0.74894], "triangles": [4, 5, 7, 5, 6, 7, 4, 7, 3, 7, 8, 3, 3, 8, 16, 3, 18, 17, 18, 3, 16, 9, 16, 8, 16, 9, 18, 18, 9, 17, 3, 17, 2, 9, 10, 15, 17, 9, 2, 15, 2, 9, 0, 2, 15, 11, 13, 0, 11, 12, 13, 13, 14, 0, 10, 11, 0, 0, 15, 10, 2, 0, 1], "vertices": [3, 9, -25.78, 16.18, 0.76428, 10, -25.78, 16.18, 0.23526, 11, -53.29, 16.18, 0.00046, 3, 9, -14.71, 22.92, 0.55765, 10, -14.71, 22.92, 0.43833, 11, -42.21, 22.92, 0.00401, 3, 9, -0.92, 22.2, 0.37391, 10, -0.92, 22.2, 0.60972, 11, -28.42, 22.2, 0.01637, 4, 10, 55.4, 12.33, 0.0011, 11, 27.9, 12.33, 0.16294, 12, 11.4, 12.33, 0.36463, 13, 0.4, 12.33, 0.47133, 4, 10, 63.58, 9.92, 0.0002, 11, 36.08, 9.92, 0.087, 12, 19.58, 9.92, 0.33253, 13, 8.58, 9.92, 0.58027, 4, 10, 69.21, 4.63, 0.00017, 11, 41.71, 4.63, 0.08414, 12, 25.21, 4.63, 0.33147, 13, 14.21, 4.63, 0.58422, 4, 10, 69.21, -4.28, 0.00015, 11, 41.71, -4.28, 0.08583, 12, 25.21, -4.28, 0.33997, 13, 14.21, -4.28, 0.57405, 4, 10, 62.14, -9.82, 0.00041, 11, 34.64, -9.82, 0.11228, 12, 18.14, -9.82, 0.35444, 13, 7.14, -9.82, 0.53286, 4, 10, 54.92, -11.5, 0.00104, 11, 27.42, -11.5, 0.16966, 12, 10.92, -11.5, 0.38503, 13, -0.08, -11.5, 0.44426, 4, 9, 29.09, -16.07, 0.00576, 10, 29.09, -16.07, 0.54396, 11, 1.59, -16.07, 0.43234, 12, -14.91, -16.07, 0.01795, 3, 9, -10.62, -22.84, 0.5117, 10, -10.62, -22.84, 0.48019, 11, -38.12, -22.84, 0.00812, 3, 9, -19.77, -20.17, 0.67075, 10, -19.77, -20.17, 0.32725, 11, -47.27, -20.17, 0.002, 3, 9, -28.19, -14.15, 0.85056, 10, -28.19, -14.15, 0.14931, 11, -55.69, -14.15, 0.00013, 3, 9, -32.79, -4.28, 0.92572, 10, -32.79, -4.28, 0.07427, 11, -60.29, -4.28, 1e-05, 3, 9, -32.79, 9.44, 0.89224, 10, -32.79, 9.44, 0.10769, 11, -60.29, 9.44, 7e-05, 4, 9, 3.82, -20.17, 0.2939, 10, 3.82, -20.17, 0.67042, 11, -23.68, -20.17, 0.03565, 12, -40.18, -20.17, 3e-05, 4, 10, 44.09, -12.95, 0.03241, 11, 16.59, -12.95, 0.50059, 12, 0.09, -12.95, 0.36001, 13, -10.91, -12.95, 0.107, 5, 9, 28.92, 16.9, 0.0043, 10, 28.92, 16.9, 0.52905, 11, 1.42, 16.9, 0.44088, 12, -15.08, 16.9, 0.02567, 13, -26.08, 16.9, 0.0001, 4, 10, 43.61, 14.01, 0.0452, 11, 16.11, 14.01, 0.50629, 12, -0.4, 14.01, 0.34492, 13, -11.4, 14.01, 0.10359], "hull": 15, "edges": [20, 22, 22, 24, 24, 26, 26, 28, 28, 0, 0, 2, 20, 30, 30, 18, 18, 32, 32, 16, 16, 14, 14, 12, 2, 4, 4, 34, 34, 36, 36, 6, 6, 8, 10, 12, 8, 10, 4, 6, 16, 18, 18, 20], "width": 46, "height": 102}}, "leg_big_outline2": {"leg_big_outline": {"type": "mesh", "uvs": [0.84826, 0.06864, 0.99477, 0.17719, 0.97907, 0.31238, 0.90724, 0.49726, 0.85982, 0.61932, 0.83682, 0.67851, 0.81003, 0.74748, 0.79224, 0.79325, 0.76454, 0.86457, 0.71221, 0.9448, 0.5971, 1, 0.4035, 1, 0.28315, 0.93064, 0.2465, 0.85986, 0.23081, 0.80676, 0.21513, 0.75366, 0.18111, 0.68016, 0.1471, 0.60666, 0.10263, 0.48277, 0.05815, 0.35889, 0, 0.2173, 0.05815, 0.12763, 0.18896, 0.04504, 0.4035, 0, 0.70175, 0], "triangles": [2, 0, 1, 0, 19, 20, 20, 21, 0, 23, 24, 0, 21, 22, 23, 21, 23, 0, 2, 19, 0, 18, 19, 3, 2, 3, 19, 17, 18, 3, 4, 17, 3, 4, 16, 17, 6, 15, 5, 7, 15, 6, 5, 15, 16, 14, 15, 7, 5, 16, 4, 8, 13, 14, 7, 8, 14, 12, 13, 8, 9, 12, 8, 10, 11, 12, 9, 10, 12], "vertices": [3, 21, -55.25, -15.57, 0.00018, 20, -27.75, -15.57, 0.17748, 19, -27.75, -15.57, 0.82234, 3, 21, -44.18, -22.31, 0.00385, 20, -16.68, -22.31, 0.4269, 19, -16.68, -22.31, 0.56925, 3, 21, -30.39, -21.58, 0.01678, 20, -2.89, -21.58, 0.58843, 19, -2.89, -21.58, 0.39479, 4, 22, -28.03, -18.28, 0.00045, 21, -11.53, -18.28, 0.13039, 20, 15.97, -18.28, 0.76827, 19, 15.97, -18.28, 0.10089, 4, 22, -15.58, -16.1, 0.01619, 21, 0.92, -16.1, 0.41383, 20, 28.42, -16.1, 0.56212, 19, 28.42, -16.1, 0.00786, 5, 23, -20.55, -15.04, 0.00273, 22, -9.55, -15.04, 0.07923, 21, 6.96, -15.04, 0.60143, 20, 34.46, -15.04, 0.3161, 19, 34.46, -15.04, 0.0005, 4, 23, -13.51, -13.81, 0.0481, 22, -2.51, -13.81, 0.27846, 21, 13.99, -13.81, 0.59931, 20, 41.49, -13.81, 0.07413, 4, 23, -8.84, -12.99, 0.16239, 22, 2.16, -12.99, 0.38728, 21, 18.66, -12.99, 0.43193, 20, 46.16, -12.99, 0.01839, 4, 23, -1.57, -11.72, 0.39368, 22, 9.43, -11.72, 0.39284, 21, 25.93, -11.72, 0.21108, 20, 53.43, -11.72, 0.00241, 4, 23, 6.62, -9.31, 0.52819, 22, 17.62, -9.31, 0.35608, 21, 34.12, -9.31, 0.11528, 20, 61.62, -9.31, 0.00044, 4, 23, 12.25, -4.01, 0.57405, 22, 23.25, -4.01, 0.33997, 21, 39.75, -4.01, 0.08583, 20, 67.25, -4.01, 0.00015, 4, 23, 12.25, 4.89, 0.58442, 22, 23.25, 4.89, 0.3313, 21, 39.75, 4.89, 0.08411, 20, 67.25, 4.89, 0.00017, 4, 23, 5.17, 10.43, 0.53624, 22, 16.17, 10.43, 0.34553, 21, 32.67, 10.43, 0.11766, 20, 60.17, 10.43, 0.00056, 4, 23, -2.05, 12.11, 0.3917, 22, 8.95, 12.11, 0.37819, 21, 25.45, 12.11, 0.22662, 20, 52.95, 12.11, 0.0035, 4, 23, -7.47, 12.83, 0.21402, 22, 3.54, 12.83, 0.38506, 21, 20.04, 12.83, 0.38505, 20, 47.54, 12.83, 0.01587, 4, 23, -12.88, 13.56, 0.06871, 22, -1.88, 13.56, 0.30308, 21, 14.62, 13.56, 0.556, 20, 42.12, 13.56, 0.0722, 5, 23, -20.38, 15.12, 0.00425, 22, -9.38, 15.12, 0.09704, 21, 7.12, 15.12, 0.57641, 20, 34.62, 15.12, 0.32176, 19, 34.62, 15.12, 0.00054, 4, 22, -16.87, 16.69, 0.01796, 21, -0.37, 16.69, 0.38699, 20, 27.13, 16.69, 0.58484, 19, 27.13, 16.69, 0.01021, 4, 22, -29.51, 18.73, 0.00072, 21, -13.01, 18.73, 0.10167, 20, 14.49, 18.73, 0.77245, 19, 14.49, 18.73, 0.12515, 4, 22, -42.15, 20.78, 2e-05, 21, -25.65, 20.78, 0.02097, 20, 1.86, 20.78, 0.64035, 19, 1.86, 20.78, 0.33866, 3, 21, -40.09, 23.45, 0.00528, 20, -12.59, 23.45, 0.45587, 19, -12.59, 23.45, 0.53886, 3, 21, -49.23, 20.78, 0.00215, 20, -21.73, 20.78, 0.33766, 19, -21.73, 20.78, 0.66019, 3, 21, -57.66, 14.76, 0.00016, 20, -30.16, 14.76, 0.13944, 19, -30.16, 14.76, 0.8604, 3, 21, -62.25, 4.89, 3e-05, 20, -34.75, 4.89, 0.0792, 19, -34.75, 4.89, 0.92077, 3, 21, -62.25, -8.83, 4e-05, 20, -34.75, -8.83, 0.09396, 19, -34.75, -8.83, 0.906], "hull": 25, "edges": [40, 42, 42, 44, 44, 46, 46, 48, 48, 0, 0, 2, 40, 38, 26, 24, 24, 22, 2, 4, 16, 18, 20, 22, 18, 20, 30, 32, 32, 34, 26, 28, 28, 30, 10, 12, 12, 14, 14, 16, 8, 10, 34, 36, 36, 38, 4, 6, 6, 8], "width": 46, "height": 102}}, "leg_small": {"leg_small": {"type": "mesh", "uvs": [0.87075, 0.08626, 1, 0.17493, 1, 0.25473, 0.89137, 0.48315, 0.86562, 0.5373, 0.83917, 0.59508, 0.80607, 0.66734, 0.77298, 0.73961, 0.73179, 0.80643, 0.69061, 0.87325, 0.66237, 0.91675, 0.60831, 1, 0.4282, 1, 0.35677, 0.91347, 0.32402, 0.87379, 0.25989, 0.76987, 0.2378, 0.73406, 0.19335, 0.66039, 0.1489, 0.58671, 0.11942, 0.53785, 0.09853, 0.48754, 0, 0.25029, 0, 0.16828, 0.14517, 0.06852, 0.33557, 0, 0.66779, 0], "triangles": [11, 12, 10, 12, 13, 10, 10, 13, 9, 13, 14, 9, 9, 14, 8, 8, 14, 15, 8, 15, 7, 15, 16, 7, 7, 16, 6, 16, 17, 6, 6, 17, 5, 17, 18, 5, 5, 18, 4, 18, 19, 4, 19, 20, 4, 4, 20, 3, 3, 20, 2, 21, 2, 20, 24, 25, 0, 0, 21, 23, 23, 24, 0, 0, 2, 21, 0, 1, 2, 21, 22, 23], "vertices": [3, 15, -8.45, 12.67, 0.42526, 16, -29.81, 12.67, 0.19943, 14, -8.45, 12.67, 0.37531, 4, 15, -2.61, 16.16, 0.4177, 16, -23.96, 16.16, 0.27994, 17, -36.78, 16.16, 0.02053, 14, -2.61, 16.16, 0.28183, 5, 15, 2.58, 16.04, 0.38112, 16, -18.78, 16.04, 0.35005, 17, -31.59, 16.04, 0.07185, 18, -40.13, 16.04, 0.01574, 14, 2.58, 16.04, 0.18123, 5, 15, 17.35, 12.65, 0.33495, 16, -4, 12.65, 0.3903, 17, -16.82, 12.65, 0.13048, 18, -25.36, 12.65, 0.03521, 14, 17.35, 12.65, 0.10907, 5, 15, 20.85, 11.85, 0.2888, 16, -0.5, 11.85, 0.38977, 17, -13.32, 11.85, 0.17945, 18, -21.86, 11.85, 0.07082, 14, 20.85, 11.85, 0.07116, 5, 15, 24.59, 11.02, 0.25094, 16, 3.23, 11.02, 0.37325, 17, -9.58, 11.02, 0.21124, 18, -18.12, 11.02, 0.11087, 14, 24.59, 11.02, 0.05371, 5, 15, 29.26, 9.98, 0.20159, 16, 7.91, 9.98, 0.34531, 17, -4.9, 9.98, 0.24933, 18, -13.45, 9.98, 0.16738, 14, 29.26, 9.98, 0.03639, 5, 15, 33.94, 8.95, 0.1498, 16, 12.58, 8.95, 0.29956, 17, -0.23, 8.95, 0.27507, 18, -8.77, 8.95, 0.25452, 14, 33.94, 8.95, 0.02105, 5, 15, 38.25, 7.69, 0.09911, 16, 16.9, 7.69, 0.238, 17, 4.09, 7.69, 0.28571, 18, -4.46, 7.69, 0.36825, 14, 38.25, 7.69, 0.00892, 5, 15, 42.57, 6.44, 0.06039, 16, 21.21, 6.44, 0.17371, 17, 8.4, 6.44, 0.27571, 18, -0.14, 6.44, 0.48722, 14, 42.57, 6.44, 0.00297, 4, 15, 45.38, 5.58, 0.02735, 16, 24.02, 5.58, 0.11089, 17, 11.21, 5.58, 0.2537, 18, 2.67, 5.58, 0.60807, 4, 15, 50.75, 3.94, 0.01084, 16, 29.4, 3.94, 0.07385, 17, 16.58, 3.94, 0.23317, 18, 8.04, 3.94, 0.68214, 4, 15, 50.63, -1.1, 0.01132, 16, 29.28, -1.1, 0.07492, 17, 16.47, -1.1, 0.23376, 18, 7.93, -1.1, 0.68, 4, 15, 44.97, -2.97, 0.02783, 16, 23.61, -2.97, 0.11196, 17, 10.8, -2.97, 0.25429, 18, 2.26, -2.97, 0.60593, 5, 15, 42.37, -3.82, 0.06868, 16, 21.01, -3.82, 0.18735, 17, 8.2, -3.82, 0.27702, 18, -0.34, -3.82, 0.46228, 14, 42.37, -3.82, 0.00466, 5, 15, 35.57, -5.46, 0.1066, 16, 14.22, -5.46, 0.25057, 17, 1.4, -5.46, 0.28644, 18, -7.14, -5.46, 0.34545, 14, 35.57, -5.46, 0.01094, 5, 15, 33.23, -6.03, 0.15756, 16, 11.87, -6.03, 0.31264, 17, -0.94, -6.03, 0.27509, 18, -9.48, -6.03, 0.23069, 14, 33.23, -6.03, 0.02402, 5, 15, 28.41, -7.16, 0.20239, 16, 7.06, -7.16, 0.34684, 17, -5.75, -7.16, 0.24724, 18, -14.3, -7.16, 0.16428, 14, 28.41, -7.16, 0.03925, 5, 15, 23.6, -8.29, 0.25091, 16, 2.24, -8.29, 0.37478, 17, -10.57, -8.29, 0.20915, 18, -19.11, -8.29, 0.10777, 14, 23.6, -8.29, 0.05739, 5, 15, 20.4, -9.04, 0.2866, 16, -0.95, -9.04, 0.39092, 17, -13.77, -9.04, 0.17869, 18, -22.31, -9.04, 0.06908, 14, 20.4, -9.04, 0.07471, 5, 15, 17.12, -9.55, 0.32948, 16, -4.24, -9.55, 0.39042, 17, -17.05, -9.55, 0.13111, 18, -25.59, -9.55, 0.03553, 14, 17.12, -9.55, 0.11346, 5, 15, 1.64, -11.95, 0.37361, 16, -19.72, -11.95, 0.35018, 17, -32.53, -11.95, 0.07249, 18, -41.07, -11.95, 0.01606, 14, 1.64, -11.95, 0.18767, 4, 15, -3.69, -11.83, 0.40928, 16, -25.05, -11.83, 0.27994, 17, -37.86, -11.83, 0.02053, 14, -3.69, -11.83, 0.29025, 3, 15, -10.08, -7.61, 0.41814, 16, -31.43, -7.61, 0.19943, 14, -10.08, -7.61, 0.38243, 3, 15, -14.41, -2.18, 0.41478, 16, -35.76, -2.18, 0.15171, 14, -14.41, -2.18, 0.43351, 3, 15, -14.19, 7.12, 0.41759, 16, -35.55, 7.12, 0.15171, 14, -14.19, 7.12, 0.4307], "hull": 26, "edges": [22, 24, 42, 44, 44, 46, 46, 48, 48, 50, 50, 0, 4, 2, 0, 2, 36, 38, 8, 10, 32, 34, 34, 36, 10, 12, 12, 14, 28, 30, 30, 32, 14, 16, 16, 18, 24, 26, 26, 28, 18, 20, 20, 22, 38, 40, 40, 42, 4, 6, 6, 8], "width": 28, "height": 65}}, "leg_small3": {"leg_small": {"type": "mesh", "uvs": [0.87075, 0.08626, 1, 0.17493, 1, 0.25473, 0.90521, 0.45406, 0.86562, 0.5373, 0.8193, 0.63846, 0.77298, 0.73961, 0.73179, 0.80643, 0.69061, 0.87325, 0.60831, 1, 0.4282, 1, 0.32402, 0.87379, 0.28091, 0.80393, 0.2378, 0.73406, 0.17861, 0.63596, 0.11942, 0.53785, 0.09046, 0.46812, 0, 0.25029, 0, 0.16828, 0.14517, 0.06852, 0.33557, 0, 0.66779, 0, 0.44486, 0.46245, 0.44188, 0.54827, 0.44586, 0.64186, 0.45113, 0.74731, 0.4694, 0.83138, 0.463, 0.8856], "triangles": [0, 20, 21, 20, 0, 19, 10, 27, 9, 9, 27, 8, 10, 11, 27, 27, 26, 8, 27, 11, 26, 11, 12, 26, 8, 26, 7, 12, 25, 26, 26, 25, 7, 7, 25, 6, 12, 13, 25, 13, 24, 25, 25, 24, 6, 6, 24, 5, 13, 14, 24, 14, 23, 24, 24, 23, 5, 5, 23, 4, 14, 15, 23, 23, 22, 4, 23, 15, 22, 15, 16, 22, 4, 22, 3, 16, 17, 22, 22, 17, 2, 17, 0, 2, 3, 22, 2, 19, 0, 17, 0, 1, 2, 17, 18, 19], "vertices": [3, 25, -8.45, 12.67, 0.43285, 26, -29.81, 12.67, 0.01448, 24, -8.45, 12.67, 0.55267, 3, 25, -2.61, 16.16, 0.60668, 26, -23.96, 16.16, 0.06397, 24, -2.61, 16.16, 0.32935, 4, 25, 2.58, 16.04, 0.67671, 26, -18.78, 16.04, 0.16699, 27, -31.59, 16.04, 0.01026, 24, 2.58, 16.04, 0.14603, 4, 25, 15.47, 13.08, 0.6059, 26, -5.89, 13.08, 0.29661, 27, -18.7, 13.08, 0.05132, 24, 15.47, 13.08, 0.04617, 5, 25, 20.85, 11.85, 0.43903, 26, -0.5, 11.85, 0.39948, 27, -13.32, 11.85, 0.14222, 28, -21.86, 11.85, 0.00974, 24, 20.85, 11.85, 0.00954, 5, 25, 27.4, 10.4, 0.25997, 26, 6.04, 10.4, 0.42006, 27, -6.77, 10.4, 0.26974, 28, -15.31, 10.4, 0.04868, 24, 27.4, 10.4, 0.00156, 5, 25, 33.94, 8.95, 0.12136, 26, 12.58, 8.95, 0.35226, 27, -0.23, 8.95, 0.37228, 28, -8.77, 8.95, 0.15386, 24, 33.94, 8.95, 0.00024, 4, 25, 38.25, 7.69, 0.04127, 26, 16.9, 7.69, 0.23291, 27, 4.09, 7.69, 0.3927, 28, -4.46, 7.69, 0.33312, 4, 25, 42.57, 6.44, 0.00825, 26, 21.21, 6.44, 0.11757, 27, 8.4, 6.44, 0.32667, 28, -0.14, 6.44, 0.54751, 3, 26, 29.4, 3.94, 0.04952, 27, 16.58, 3.94, 0.25481, 28, 8.04, 3.94, 0.69566, 3, 26, 29.28, -1.1, 0.04952, 27, 16.47, -1.1, 0.25481, 28, 7.93, -1.1, 0.69566, 4, 25, 42.37, -3.82, 0.00825, 26, 21.01, -3.82, 0.11757, 27, 8.2, -3.82, 0.32667, 28, -0.34, -3.82, 0.54751, 4, 25, 37.8, -4.92, 0.04127, 26, 16.44, -4.92, 0.23291, 27, 3.63, -4.92, 0.3927, 28, -4.91, -4.92, 0.33312, 5, 25, 33.23, -6.03, 0.11974, 26, 11.87, -6.03, 0.35333, 27, -0.94, -6.03, 0.37228, 28, -9.48, -6.03, 0.15386, 24, 33.23, -6.03, 0.0008, 5, 25, 26.82, -7.53, 0.25263, 26, 5.46, -7.53, 0.42327, 27, -7.35, -7.53, 0.26974, 28, -15.89, -7.53, 0.04868, 24, 26.82, -7.53, 0.00569, 5, 25, 20.4, -9.04, 0.41828, 26, -0.95, -9.04, 0.4059, 27, -13.77, -9.04, 0.14222, 28, -22.31, -9.04, 0.00974, 24, 20.4, -9.04, 0.02387, 4, 25, 15.85, -9.75, 0.56768, 26, -5.5, -9.75, 0.30409, 27, -18.32, -9.75, 0.05132, 24, 15.85, -9.75, 0.07691, 4, 25, 1.64, -11.95, 0.62437, 26, -19.72, -11.95, 0.17341, 27, -32.53, -11.95, 0.01026, 24, 1.64, -11.95, 0.19196, 3, 25, -3.69, -11.83, 0.55433, 26, -25.05, -11.83, 0.06718, 24, -3.69, -11.83, 0.37849, 3, 25, -10.08, -7.61, 0.39475, 26, -31.43, -7.61, 0.01555, 24, -10.08, -7.61, 0.5897, 2, 25, -14.41, -2.18, 0.27004, 24, -14.41, -2.18, 0.72996, 2, 25, -14.19, 7.12, 0.28345, 24, -14.19, 7.12, 0.71655, 3, 25, 15.71, 0.18, 0.5768, 26, -5.64, 0.18, 0.40819, 24, 15.71, 0.18, 0.01501, 4, 25, 21.29, -0.03, 0.40275, 26, -0.07, -0.03, 0.45334, 27, -12.88, -0.03, 0.13628, 24, 21.29, -0.03, 0.00763, 4, 25, 27.37, -0.06, 0.20224, 26, 6.02, -0.06, 0.44954, 27, -6.79, -0.06, 0.34439, 24, 27.37, -0.06, 0.00383, 5, 25, 34.23, -0.07, 0.07143, 26, 12.87, -0.07, 0.30293, 27, 0.06, -0.07, 0.45427, 28, -8.48, -0.07, 0.17001, 24, 34.23, -0.07, 0.00135, 5, 25, 39.7, 0.31, 0.02387, 26, 18.35, 0.31, 0.10121, 27, 5.54, 0.31, 0.4676, 28, -3.01, 0.31, 0.40687, 24, 39.7, 0.31, 0.00045, 5, 25, 43.22, 0.05, 0.01566, 26, 21.87, 0.05, 0.06641, 27, 9.06, 0.05, 0.32088, 28, 0.51, 0.05, 0.59675, 24, 43.22, 0.05, 0.0003], "hull": 22, "edges": [22, 20, 18, 20, 18, 16, 34, 36, 36, 38, 38, 40, 40, 42, 42, 0, 4, 2, 0, 2, 8, 10, 10, 12, 26, 28, 28, 30, 12, 14, 14, 16, 22, 24, 24, 26, 4, 6, 6, 8, 30, 32, 32, 34], "width": 28, "height": 65}}, "leg_small_outline": {"leg_small_outline": {"type": "mesh", "uvs": [0.89163, 0.11368, 1, 0.2459, 0.95642, 0.39134, 0.91284, 0.53678, 0.88102, 0.62712, 0.84921, 0.71747, 0.82376, 0.77807, 0.7983, 0.82986, 0.72099, 0.98017, 0.30199, 0.98898, 0.21715, 0.83206, 0.20867, 0.78578, 0.18746, 0.72629, 0.14928, 0.63594, 0.1111, 0.54559, 0.05555, 0.4277, 0, 0.3098, 0, 0.23708, 0.13231, 0.10707, 0.33169, 0, 0.66584, 0], "triangles": [9, 10, 8, 8, 10, 7, 10, 11, 7, 7, 11, 6, 11, 12, 6, 6, 12, 5, 12, 13, 5, 5, 13, 4, 13, 14, 4, 4, 14, 3, 3, 14, 2, 15, 2, 14, 18, 0, 16, 0, 18, 20, 18, 19, 20, 0, 2, 15, 0, 15, 16, 2, 0, 1, 16, 17, 18], "vertices": [4, 14, -11.18, 18.02, 0.3699, 15, -11.18, 18.02, 0.42483, 16, -32.54, 18.02, 0.20408, 17, -45.35, 18.02, 0.00119, 5, 14, -0.91, 22.12, 0.2515, 15, -0.91, 22.12, 0.40667, 16, -22.26, 22.12, 0.30108, 17, -35.07, 22.12, 0.036, 18, -43.61, 22.12, 0.00475, 5, 14, 10.25, 20.11, 0.14996, 15, 10.25, 20.11, 0.36111, 16, -11.1, 20.11, 0.36749, 17, -23.92, 20.11, 0.09726, 18, -32.46, 20.11, 0.02418, 5, 14, 21.41, 18.11, 0.08024, 15, 21.41, 18.11, 0.29986, 16, 0.05, 18.11, 0.3899, 17, -12.76, 18.11, 0.16772, 18, -21.3, 18.11, 0.06229, 5, 14, 28.33, 16.68, 0.04493, 15, 28.33, 16.68, 0.22591, 16, 6.98, 16.68, 0.35907, 17, -5.84, 16.68, 0.23056, 18, -14.38, 16.68, 0.13953, 5, 14, 35.26, 15.24, 0.02129, 15, 35.26, 15.24, 0.15062, 16, 13.9, 15.24, 0.30028, 17, 1.09, 15.24, 0.27466, 18, -7.45, 15.24, 0.25314, 5, 14, 39.9, 14.12, 0.0095, 15, 39.9, 14.12, 0.10153, 16, 18.54, 14.12, 0.24093, 17, 5.73, 14.12, 0.28521, 18, -2.81, 14.12, 0.36283, 5, 14, 43.86, 13.01, 0.00376, 15, 43.86, 13.01, 0.06549, 16, 22.51, 13.01, 0.18219, 17, 9.69, 13.01, 0.27703, 18, 1.15, 13.01, 0.47153, 4, 15, 55.36, 9.65, 0.01084, 16, 34, 9.65, 0.07385, 17, 21.19, 9.65, 0.23317, 18, 12.65, 9.65, 0.68214, 4, 15, 55.65, -7.13, 0.01132, 16, 34.29, -7.13, 0.07492, 17, 21.48, -7.13, 0.23376, 18, 12.94, -7.13, 0.68, 5, 14, 43.49, -10.24, 0.00503, 15, 43.49, -10.24, 0.07091, 16, 22.13, -10.24, 0.19107, 17, 9.32, -10.24, 0.27757, 18, 0.78, -10.24, 0.45541, 5, 14, 39.92, -10.49, 0.0082, 15, 39.92, -10.49, 0.09007, 16, 18.56, -10.49, 0.22301, 17, 5.75, -10.49, 0.28233, 18, -2.79, -10.49, 0.39639, 5, 14, 35.32, -11.24, 0.01962, 15, 35.32, -11.24, 0.14041, 16, 13.96, -11.24, 0.29176, 17, 1.15, -11.24, 0.27891, 18, -7.39, -11.24, 0.2693, 5, 14, 28.33, -12.6, 0.04412, 15, 28.33, -12.6, 0.21541, 16, 6.97, -12.6, 0.35434, 17, -5.84, -12.6, 0.23702, 18, -14.38, -12.6, 0.14911, 5, 14, 21.34, -13.97, 0.07471, 15, 21.34, -13.97, 0.2866, 16, -0.02, -13.97, 0.39092, 17, -12.83, -13.97, 0.17869, 18, -21.37, -13.97, 0.06908, 5, 14, 12.21, -15.98, 0.14109, 15, 12.21, -15.98, 0.34591, 16, -9.14, -15.98, 0.37543, 17, -21.96, -15.98, 0.10928, 18, -30.5, -15.98, 0.02828, 5, 14, 3.08, -17.99, 0.18528, 15, 3.08, -17.99, 0.37219, 16, -18.27, -17.99, 0.35147, 17, -31.08, -17.99, 0.07437, 18, -39.63, -17.99, 0.01669, 5, 14, -2.51, -17.86, 0.2649, 15, -2.51, -17.86, 0.40046, 16, -23.87, -17.86, 0.2973, 17, -36.68, -17.86, 0.03337, 18, -45.22, -17.86, 0.00397, 4, 14, -12.4, -12.33, 0.37445, 15, -12.4, -12.33, 0.41738, 16, -33.75, -12.33, 0.2064, 17, -46.57, -12.33, 0.00178, 3, 14, -20.46, -4.17, 0.43351, 15, -20.46, -4.17, 0.41478, 16, -41.81, -4.17, 0.15171, 3, 14, -20.15, 9.2, 0.4307, 15, -20.15, 9.2, 0.41759, 16, -41.5, 9.2, 0.15171], "hull": 21, "edges": [34, 36, 36, 38, 38, 40, 40, 0, 0, 2, 32, 34, 20, 18, 16, 18, 16, 14, 24, 26, 26, 28, 6, 8, 8, 10, 20, 22, 22, 24, 10, 12, 12, 14, 28, 30, 30, 32, 2, 4, 4, 6], "width": 40, "height": 77}}, "leg_small_outline2": {"leg_small_outline": {"type": "mesh", "uvs": [0.89163, 0.11368, 1, 0.2459, 0.95642, 0.39134, 0.91284, 0.53678, 0.88102, 0.62712, 0.84921, 0.71747, 0.82376, 0.77807, 0.7983, 0.82986, 0.72099, 0.98017, 0.30199, 0.98898, 0.21715, 0.83206, 0.20867, 0.78578, 0.18746, 0.72629, 0.14928, 0.63594, 0.1111, 0.54559, 0.05555, 0.4277, 0, 0.3098, 0, 0.23708, 0.13231, 0.10707, 0.33169, 0, 0.66584, 0], "triangles": [18, 19, 20, 0, 18, 20, 15, 2, 14, 3, 14, 2, 16, 17, 18, 2, 0, 1, 0, 15, 16, 0, 2, 15, 18, 0, 16, 4, 14, 3, 13, 14, 4, 5, 13, 4, 12, 13, 5, 6, 12, 5, 11, 12, 6, 7, 11, 6, 10, 11, 7, 8, 10, 7, 9, 10, 8], "vertices": [3, 26, -32.94, -15.22, 0.03533, 25, -11.59, -15.22, 0.45589, 24, -11.59, -15.22, 0.50878, 4, 27, -35.47, -19.32, 0.00493, 26, -22.66, -19.32, 0.11815, 25, -1.31, -19.32, 0.58793, 24, -1.31, -19.32, 0.28899, 4, 27, -24.32, -17.31, 0.03108, 26, -11.51, -17.31, 0.23966, 25, 9.85, -17.31, 0.59563, 24, 9.85, -17.31, 0.13363, 5, 28, -21.71, -15.31, 0.00896, 27, -13.16, -15.31, 0.135, 26, -0.35, -15.31, 0.3978, 25, 21, -15.31, 0.43015, 24, 21, -15.31, 0.02808, 5, 28, -14.78, -13.88, 0.0465, 27, -6.24, -13.88, 0.2626, 26, 6.57, -13.88, 0.42229, 25, 27.93, -13.88, 0.2619, 24, 27.93, -13.88, 0.00671, 5, 28, -7.86, -12.44, 0.15674, 27, 0.69, -12.44, 0.3726, 26, 13.5, -12.44, 0.35139, 25, 34.85, -12.44, 0.11847, 24, 34.85, -12.44, 0.00079, 4, 28, -3.21, -11.32, 0.34008, 27, 5.33, -11.32, 0.39055, 26, 18.14, -11.32, 0.22917, 25, 39.5, -11.32, 0.0402, 4, 28, 0.75, -10.21, 0.52772, 27, 9.29, -10.21, 0.33276, 26, 22.1, -10.21, 0.12821, 25, 43.46, -10.21, 0.0113, 3, 28, 12.25, -6.85, 0.69566, 27, 20.79, -6.85, 0.25481, 26, 33.6, -6.85, 0.04952, 3, 28, 12.54, 9.93, 0.69566, 27, 21.08, 9.93, 0.25481, 26, 33.89, 9.93, 0.04952, 4, 28, 0.38, 13.04, 0.48345, 27, 8.92, 13.04, 0.3464, 26, 21.73, 13.04, 0.15203, 25, 43.09, 13.04, 0.01812, 5, 28, -3.19, 13.29, 0.31919, 27, 5.35, 13.29, 0.39111, 26, 18.16, 13.29, 0.24219, 25, 39.52, 13.29, 0.0475, 24, 39.52, 13.29, 2e-05, 5, 28, -7.79, 14.04, 0.15159, 27, 0.75, 14.04, 0.37006, 26, 13.56, 14.04, 0.35373, 25, 34.92, 14.04, 0.12436, 24, 34.92, 14.04, 0.00027, 5, 28, -14.78, 15.4, 0.0454, 27, -6.24, 15.4, 0.25902, 26, 6.57, 15.4, 0.41833, 25, 27.93, 15.4, 0.27503, 24, 27.93, 15.4, 0.00223, 5, 28, -21.77, 16.77, 0.00794, 27, -13.23, 16.77, 0.12549, 26, -0.42, 16.77, 0.38054, 25, 20.94, 16.77, 0.46975, 24, 20.94, 16.77, 0.01628, 4, 27, -22.36, 18.78, 0.0363, 26, -9.55, 18.78, 0.24918, 25, 11.81, 18.78, 0.63181, 24, 11.81, 18.78, 0.08271, 4, 27, -31.49, 20.79, 0.01025, 26, -18.67, 20.79, 0.16689, 25, 2.68, 20.79, 0.67664, 24, 2.68, 20.79, 0.14622, 3, 26, -24.27, 20.66, 0.06397, 25, -2.92, 20.66, 0.60668, 24, -2.92, 20.66, 0.32935, 3, 26, -34.16, 15.13, 0.01192, 25, -12.8, 15.13, 0.40642, 24, -12.8, 15.13, 0.58166, 2, 25, -20.86, 6.97, 0.28301, 24, -20.86, 6.97, 0.71699, 2, 25, -20.55, -6.39, 0.27004, 24, -20.55, -6.39, 0.72996], "hull": 21, "edges": [34, 36, 36, 38, 38, 40, 40, 0, 0, 2, 32, 34, 20, 18, 16, 18, 16, 14, 24, 26, 26, 28, 6, 8, 8, 10, 20, 22, 22, 24, 10, 12, 12, 14, 28, 30, 30, 32, 2, 4, 4, 6], "width": 40, "height": 77}}, "mouth": {"mouth": {"type": "mesh", "uvs": [1, 1, 0.97398, 1, 0.94796, 1, 0.91937, 1, 0.89593, 1, 0.87172, 1, 0.83179, 1, 0.79185, 1, 0.70147, 1, 0.61109, 1, 0.49943, 1, 0.39637, 1, 0.29332, 1, 0.23637, 1, 0.17547, 1, 0.10839, 0.99333, 0.08442, 1, 0.06148, 1, 0.0434, 1, 0.02736, 1, 0, 1, 0, 0, 0.02677, 0, 0.04506, 0, 0.05912, 0, 0.08354, 0, 0.10795, 0, 0.12883, 0, 0.17338, 0, 0.23882, 0, 0.29663, 0, 0.39803, 0, 0.49943, 0, 0.60612, 0, 0.69838, 0, 0.79063, 0, 0.83118, 0, 0.87172, 0, 0.89531, 0, 0.92149, 0, 0.94766, 0, 0.97383, 0, 1, 0], "triangles": [1, 42, 0, 2, 41, 1, 1, 41, 42, 2, 40, 41, 3, 39, 2, 39, 40, 2, 39, 3, 38, 4, 5, 38, 5, 37, 38, 3, 4, 38, 5, 6, 37, 7, 36, 6, 6, 36, 37, 8, 35, 7, 7, 35, 36, 9, 34, 8, 8, 34, 35, 10, 33, 9, 9, 33, 34, 11, 31, 10, 31, 32, 10, 10, 32, 33, 12, 30, 11, 11, 30, 31, 13, 29, 12, 12, 29, 30, 29, 13, 28, 15, 27, 14, 27, 28, 14, 13, 14, 28, 15, 26, 27, 17, 25, 16, 16, 26, 15, 16, 25, 26, 17, 18, 24, 18, 23, 24, 17, 24, 25, 23, 18, 22, 20, 22, 19, 18, 19, 22, 20, 21, 22], "vertices": [3, 41, 132.97, -3.01, 0.00083, 40, 67.39, -4.37, 0.36007, 42, 5.91, -3.35, 0.6391, 3, 41, 129.43, -3.01, 0.00068, 40, 63.85, -4.37, 0.41787, 42, 2.37, -3.35, 0.58145, 3, 41, 125.89, -3.01, 0.00042, 40, 60.31, -4.37, 0.51916, 42, -1.16, -3.35, 0.48041, 3, 41, 122, -3.01, 0.00019, 40, 56.42, -4.37, 0.62385, 42, -5.05, -3.35, 0.37595, 3, 41, 118.81, -3.01, 7e-05, 40, 53.24, -4.37, 0.70991, 42, -8.24, -3.35, 0.29002, 3, 41, 115.52, -3.01, 1e-05, 40, 49.94, -4.37, 0.78718, 42, -11.53, -3.35, 0.2128, 3, 41, 110.09, -3.01, 0, 40, 44.51, -4.37, 0.85915, 42, -16.96, -3.35, 0.14085, 2, 40, 39.08, -4.37, 0.9188, 42, -22.4, -3.35, 0.0812, 3, 41, 92.37, -3.01, 0.00049, 40, 26.79, -4.37, 0.96123, 42, -34.69, -3.35, 0.03828, 3, 41, 80.08, -3.01, 0.00414, 40, 14.5, -4.37, 0.98192, 42, -46.98, -3.35, 0.01394, 3, 41, 64.89, -3.01, 0.01787, 40, -0.69, -4.37, 0.97859, 42, -62.16, -3.35, 0.00354, 3, 41, 50.87, -3.01, 0.05304, 40, -14.7, -4.37, 0.94645, 42, -76.18, -3.35, 0.00051, 3, 41, 36.86, -3.01, 0.12, 40, -28.72, -4.37, 0.87998, 42, -90.2, -3.35, 2e-05, 3, 41, 29.11, -3.01, 0.22091, 40, -36.46, -4.37, 0.77902, 42, -97.94, -3.35, 6e-05, 3, 41, 20.83, -3.01, 0.34549, 40, -44.75, -4.37, 0.65435, 42, -106.22, -3.35, 0.00017, 3, 41, 11.71, -2.97, 0.47721, 40, -53.87, -4.33, 0.52244, 42, -115.35, -3.31, 0.00035, 3, 41, 8.45, -3.01, 0.60187, 40, -57.13, -4.37, 0.39756, 42, -118.61, -3.35, 0.00057, 3, 41, 5.33, -3.01, 0.69447, 40, -60.25, -4.37, 0.30475, 42, -121.73, -3.35, 0.00077, 3, 41, 2.87, -3.01, 0.76349, 40, -62.71, -4.37, 0.23565, 42, -124.18, -3.35, 0.00086, 3, 41, 0.69, -3.01, 0.81524, 40, -64.89, -4.37, 0.18391, 42, -126.37, -3.35, 0.00086, 3, 41, -3.03, -3.01, 0.83876, 40, -68.61, -4.37, 0.16041, 42, -130.09, -3.35, 0.00082, 3, 41, -3.03, 2.99, 0.8389, 40, -68.61, 1.63, 0.16028, 42, -130.09, 2.65, 0.00082, 3, 41, 0.61, 2.99, 0.81547, 40, -64.97, 1.63, 0.18368, 42, -126.45, 2.65, 0.00086, 3, 41, 3.09, 2.99, 0.76505, 40, -62.48, 1.63, 0.23409, 42, -123.96, 2.65, 0.00086, 3, 41, 5.01, 2.99, 0.69811, 40, -60.57, 1.63, 0.30112, 42, -122.05, 2.65, 0.00077, 3, 41, 8.33, 2.99, 0.61673, 40, -57.25, 1.63, 0.3827, 42, -118.73, 2.65, 0.00057, 3, 41, 11.65, 2.99, 0.53228, 40, -53.93, 1.63, 0.46737, 42, -115.41, 2.65, 0.00034, 3, 41, 14.49, 2.99, 0.43516, 40, -51.09, 1.63, 0.56468, 42, -112.57, 2.65, 0.00017, 3, 41, 20.55, 2.99, 0.32802, 40, -45.03, 1.63, 0.67192, 42, -106.51, 2.65, 6e-05, 3, 41, 29.45, 2.99, 0.22028, 40, -36.13, 1.63, 0.7797, 42, -97.61, 2.65, 2e-05, 3, 41, 37.31, 2.99, 0.1267, 40, -28.27, 1.63, 0.8733, 42, -89.75, 2.65, 0, 3, 41, 51.1, 2.99, 0.0596, 40, -14.48, 1.63, 0.93989, 42, -75.96, 2.65, 0.00051, 3, 41, 64.89, 2.99, 0.02161, 40, -0.69, 1.63, 0.97482, 42, -62.16, 2.65, 0.00357, 3, 41, 79.4, 2.99, 0.00543, 40, 13.82, 1.63, 0.98056, 42, -47.65, 2.65, 0.01401, 3, 41, 91.95, 2.99, 0.00075, 40, 26.37, 1.63, 0.96086, 42, -35.11, 2.65, 0.03839, 2, 40, 38.92, 1.63, 0.91873, 42, -22.56, 2.65, 0.08127, 3, 41, 110.01, 2.99, 0, 40, 44.43, 1.63, 0.85923, 42, -17.05, 2.65, 0.14077, 3, 41, 115.52, 2.99, 1e-05, 40, 49.94, 1.63, 0.78755, 42, -11.53, 2.65, 0.21243, 3, 41, 118.73, 2.99, 7e-05, 40, 53.15, 1.63, 0.71104, 42, -8.32, 2.65, 0.28889, 3, 41, 122.29, 2.99, 0.00019, 40, 56.71, 1.63, 0.62491, 42, -4.77, 2.65, 0.37489, 3, 41, 125.85, 2.99, 0.00042, 40, 60.27, 1.63, 0.52107, 42, -1.21, 2.65, 0.4785, 3, 41, 129.41, 2.99, 0.00068, 40, 63.83, 1.63, 0.41874, 42, 2.35, 2.65, 0.58058, 3, 41, 132.97, 2.99, 0.00083, 40, 67.39, 1.63, 0.36053, 42, 5.91, 2.65, 0.63864], "hull": 43, "edges": [40, 42, 0, 84, 56, 58, 26, 28, 28, 30, 58, 60, 24, 26, 60, 62, 62, 64, 20, 22, 22, 24, 64, 66, 18, 20, 66, 68, 68, 70, 14, 16, 16, 18, 42, 44, 44, 46, 36, 38, 38, 40, 44, 38, 46, 48, 34, 36, 48, 34, 48, 50, 50, 52, 30, 32, 32, 34, 50, 32, 52, 54, 54, 56, 80, 82, 82, 84, 0, 2, 2, 4, 82, 2, 76, 78, 78, 80, 4, 6, 6, 8, 78, 6, 74, 76, 8, 10, 74, 10, 70, 72, 72, 74, 10, 12, 12, 14, 72, 12], "width": 136, "height": 6}}, "pupil_l": {"pupil_l": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 30, -5.65, 6.2, 1, 1, 30, 6.34, 5.69, 1, 1, 30, 5.82, -6.3, 1, 1, 30, -6.16, -5.78, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 12, "height": 12}}, "pupil_r": {"pupil_r": {"x": 0.13, "y": -0.17, "rotation": 2.72, "width": 12, "height": 12}}, "teeth": {"teeth": {"type": "mesh", "uvs": [1, 1, 0.83739, 1, 0.60556, 0.99999, 0.39558, 0.99999, 0.23187, 0.99998, 0, 1, 0, 0, 0.23055, 0.02471, 0.40377, 0.00819, 0.60387, 0.01272, 0.83567, 0.00528, 1, 0], "triangles": [5, 7, 4, 5, 6, 7, 3, 8, 2, 8, 9, 2, 8, 3, 7, 3, 4, 7, 1, 11, 0, 2, 10, 1, 1, 10, 11, 2, 9, 10], "vertices": [3, 41, 126.97, -2.01, 0.00023, 40, 61.39, -3.37, 0.57094, 42, -0.09, -2.35, 0.42883, 2, 40, 42.04, -3.37, 0.93263, 42, -19.44, -2.35, 0.06737, 1, 40, 14.45, -3.37, 1, 1, 40, -10.54, -3.37, 1, 2, 41, 35.56, -2.01, 0.03146, 40, -30.02, -3.37, 0.96854, 3, 41, 7.97, -2.01, 0.64256, 40, -57.61, -3.37, 0.3568, 42, -119.09, -2.35, 0.00064, 3, 41, 7.97, 16.99, 0.63291, 40, -57.61, 15.63, 0.36649, 42, -119.09, 16.65, 0.00059, 2, 41, 35.4, 16.52, 0.04432, 40, -30.17, 15.16, 0.95568, 1, 40, -9.56, 15.47, 1, 1, 40, 14.25, 15.39, 1, 2, 40, 41.84, 15.53, 0.93439, 42, -19.64, 16.55, 0.06561, 3, 41, 126.97, 16.99, 0.00024, 40, 61.39, 15.63, 0.58157, 42, -0.09, 16.65, 0.4182], "hull": 12, "edges": [10, 12, 0, 22, 12, 14, 8, 10, 14, 8, 20, 22, 0, 2, 20, 2, 18, 20, 2, 4, 18, 4, 14, 16, 16, 18, 4, 6, 6, 8, 16, 6], "width": 119, "height": 19}}}}], "animations": {"t0_405c80": {"slots": {"body_outline": {"rgba": [{"color": "405c80ff"}]}, "leg_big_outline": {"rgba": [{"color": "405c80ff"}]}, "leg_big_outline2": {"rgba": [{"color": "405c80ff"}]}, "leg_small_outline": {"rgba": [{"color": "405c80ff"}]}, "leg_small_outline2": {"rgba": [{"color": "405c80ff"}]}}}, "t1_Death": {"slots": {"blot": {"rgba2": [{"light": "06aefaff", "dark": "ffffff", "curve": "stepped"}, {"time": 0.5, "light": "06aefaff", "dark": "ffffff", "curve": [0.667, 0.02, 0.833, 0.02, 0.667, 0.68, 0.833, 0.68, 0.667, 0.98, 0.833, 0.98, 0.667, 1, 0.833, 0, 0.667, 1, 0.833, 1, 0.667, 1, 0.833, 1, 0.667, 1, 0.833, 1]}, {"time": 1, "light": "06aefa00", "dark": "ffffff"}], "attachment": [{}, {"time": 0.2667, "name": "blot"}]}, "blot_drop2": {"rgba2": [{"light": "06aefaff", "dark": "ffffff", "curve": "stepped"}, {"time": 0.5, "light": "06aefaff", "dark": "ffffff", "curve": [0.556, 0.02, 0.611, 0.02, 0.556, 0.68, 0.611, 0.68, 0.556, 0.98, 0.611, 0.98, 0.556, 1, 0.611, 0, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1]}, {"time": 0.6667, "light": "06aefa00", "dark": "ffffff"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5}]}, "blot_drop3": {"rgba2": [{"light": "06aefaff", "dark": "ffffff", "curve": "stepped"}, {"time": 0.5, "light": "06aefaff", "dark": "ffffff", "curve": [0.556, 0.02, 0.611, 0.02, 0.556, 0.68, 0.611, 0.68, 0.556, 0.98, 0.611, 0.98, 0.556, 1, 0.611, 0, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1]}, {"time": 0.6667, "light": "06aefa00", "dark": "ffffff"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.6}]}, "blot_drop4": {"rgba2": [{"light": "06aefaff", "dark": "ffffff", "curve": "stepped"}, {"time": 0.5, "light": "06aefaff", "dark": "ffffff", "curve": [0.556, 0.02, 0.611, 0.02, 0.556, 0.68, 0.611, 0.68, 0.556, 0.98, 0.611, 0.98, 0.556, 1, 0.611, 0, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1]}, {"time": 0.6667, "light": "06aefa00", "dark": "ffffff"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5667}]}, "blot_drop5": {"rgba2": [{"light": "06aefaff", "dark": "ffffff", "curve": "stepped"}, {"time": 0.5, "light": "06aefaff", "dark": "ffffff", "curve": [0.556, 0.02, 0.611, 0.02, 0.556, 0.68, 0.611, 0.68, 0.556, 0.98, 0.611, 0.98, 0.556, 1, 0.611, 0, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1]}, {"time": 0.6667, "light": "06aefa00", "dark": "ffffff"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.5}]}, "blot_drop6": {"rgba2": [{"light": "06aefaff", "dark": "ffffff", "curve": "stepped"}, {"time": 0.5, "light": "06aefaff", "dark": "ffffff", "curve": [0.556, 0.02, 0.611, 0.02, 0.556, 0.68, 0.611, 0.68, 0.556, 0.98, 0.611, 0.98, 0.556, 1, 0.611, 0, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1]}, {"time": 0.6667, "light": "06aefa00", "dark": "ffffff"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5667}]}, "blot_drop7": {"rgba2": [{"light": "06aefaff", "dark": "ffffff", "curve": "stepped"}, {"time": 0.5, "light": "06aefaff", "dark": "ffffff", "curve": [0.556, 0.02, 0.611, 0.02, 0.556, 0.68, 0.611, 0.68, 0.556, 0.98, 0.611, 0.98, 0.556, 1, 0.611, 0, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1]}, {"time": 0.6667, "light": "06aefa00", "dark": "ffffff"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.6333}]}, "blot_drop8": {"rgba2": [{"light": "06aefaff", "dark": "ffffff", "curve": "stepped"}, {"time": 0.5, "light": "06aefaff", "dark": "ffffff", "curve": [0.556, 0.02, 0.611, 0.02, 0.556, 0.68, 0.611, 0.68, 0.556, 0.98, 0.611, 0.98, 0.556, 1, 0.611, 0, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1]}, {"time": 0.6667, "light": "06aefa00", "dark": "ffffff"}], "attachment": [{}, {"time": 0.3, "name": "blot_drop1"}, {"time": 0.6667}]}, "blot_drop_s1": {"rgba2": [{"light": "06aefaff", "dark": "ffffff", "curve": "stepped"}, {"time": 0.5, "light": "06aefaff", "dark": "ffffff", "curve": [0.556, 0.02, 0.611, 0.02, 0.556, 0.68, 0.611, 0.68, 0.556, 0.98, 0.611, 0.98, 0.556, 1, 0.611, 0, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1]}, {"time": 0.6667, "light": "06aefa00", "dark": "ffffff"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.4667}]}, "blot_drop_s2": {"rgba2": [{"light": "06aefaff", "dark": "ffffff", "curve": "stepped"}, {"time": 0.5, "light": "06aefaff", "dark": "ffffff", "curve": [0.556, 0.02, 0.611, 0.02, 0.556, 0.68, 0.611, 0.68, 0.556, 0.98, 0.611, 0.98, 0.556, 1, 0.611, 0, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1, 0.556, 1, 0.611, 1]}, {"time": 0.6667, "light": "06aefa00", "dark": "ffffff"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.4333}]}, "body": {"attachment": [{"time": 0.2667}]}, "body_outline": {"attachment": [{"time": 0.2667}]}, "brow_l": {"attachment": [{"time": 0.2667}]}, "brow_r": {"attachment": [{"time": 0.2667}]}, "eyelid_l_l": {"attachment": [{"time": 0.2667}]}, "eyelid_l_r": {"attachment": [{"time": 0.2667}]}, "eyelid_u_l": {"rgba": [{"color": "ffffff00"}], "attachment": [{"name": "eyelid_u_l"}, {"time": 0.2667}]}, "eyelid_u_r": {"rgba": [{"color": "ffffff00"}], "attachment": [{"time": 0.2667}]}, "eye_l": {"attachment": [{"time": 0.2667}]}, "eye_r": {"attachment": [{"time": 0.2667}]}, "leg_big": {"attachment": [{"time": 0.2667}]}, "leg_big2": {"attachment": [{"time": 0.2667}]}, "leg_big3": {"attachment": [{"time": 0.2667}]}, "leg_big_outline": {"attachment": [{"time": 0.2667}]}, "leg_big_outline2": {"attachment": [{"time": 0.2667}]}, "leg_small": {"attachment": [{"time": 0.2667}]}, "leg_small3": {"attachment": [{"time": 0.2667}]}, "leg_small_outline": {"attachment": [{"time": 0.2667}]}, "leg_small_outline2": {"attachment": [{"time": 0.2667}]}, "mouth": {"attachment": [{"time": 0.2667}]}, "pupil_l": {"attachment": [{"time": 0.2667}]}, "pupil_r": {"attachment": [{"time": 0.2667}]}, "teeth": {"attachment": [{"time": 0.2667}]}}, "bones": {"body_cntrl": {"rotate": [{"curve": [0.078, -0.54, 0.043, 16.58]}, {"time": 0.0667, "value": 11.19, "curve": [0.08, 8.25, 0.092, -18.23]}, {"time": 0.1, "value": -18.23, "curve": [0.122, -18.23, 0.144, 17.76]}, {"time": 0.1667, "value": 17.76, "curve": [0.189, 17.76, 0.211, -27.83]}, {"time": 0.2333, "value": -27.83, "curve": [0.256, -27.83, 0.278, 6.32]}, {"time": 0.3, "value": 6.32}], "translatex": [{"value": -2.66}], "translatey": [{"curve": [0.06, 3.57, 0.089, -13.68]}, {"time": 0.1333, "value": -13.68, "curve": [0.178, -13.68, 0.222, 4.35]}, {"time": 0.2667, "value": 4.35}], "scale": [{"x": 0.99, "y": 1.01, "curve": [0.022, 0.995, 0.178, 0.67, 0.022, 1.005, 0.178, 1.01]}, {"time": 0.2667, "x": 0.67, "y": 1.01}]}, "big_leg": {"rotate": [{"value": -25.89, "curve": "stepped"}, {"time": 0.0667, "value": -25.89, "curve": [0.1, -25.89, 0.133, 12.29]}, {"time": 0.1667, "value": 12.29, "curve": [0.211, 12.29, 0.256, -12.39]}, {"time": 0.3, "value": -24.73}]}, "big_leg2": {"rotate": [{"value": -18.17, "curve": "stepped"}, {"time": 0.1, "value": -18.17, "curve": [0.133, -18.17, 0.167, 20.01]}, {"time": 0.2, "value": 20.01, "curve": [0.244, 20.01, 0.289, -4.67]}, {"time": 0.3333, "value": -17.01}]}, "big_leg3": {"rotate": [{"value": -10.36, "curve": "stepped"}, {"time": 0.1333, "value": -10.36, "curve": [0.167, -10.36, 0.2, 27.82]}, {"time": 0.2333, "value": 27.82, "curve": [0.278, 27.82, 0.322, 3.14]}, {"time": 0.3667, "value": -9.2}]}, "big_leg4": {"rotate": [{"value": -6.68, "curve": "stepped"}, {"time": 0.1667, "value": -6.68, "curve": [0.2, -6.68, 0.233, 31.5]}, {"time": 0.2667, "value": 31.5, "curve": [0.311, 31.5, 0.356, 6.82]}, {"time": 0.4, "value": -5.52}]}, "body2": {"rotate": [{"value": -26.42, "curve": [0.079, -28.08, 0.067, 11.76]}, {"time": 0.1, "value": 11.76, "curve": [0.144, 11.76, 0.189, -12.92]}, {"time": 0.2333, "value": -25.25}]}, "body2b": {"rotate": [{"value": -25.94, "curve": "stepped"}, {"time": 0.1, "value": -25.94, "curve": [0.133, -25.94, 0.167, 12.24]}, {"time": 0.2, "value": 12.24, "curve": [0.244, 12.24, 0.289, -12.44]}, {"time": 0.3333, "value": -24.78}]}, "body2c": {"rotate": [{"value": -15.18, "curve": "stepped"}, {"time": 0.1333, "value": -15.18, "curve": [0.167, -15.18, 0.2, 23]}, {"time": 0.2333, "value": 23, "curve": [0.278, 23, 0.322, -1.68]}, {"time": 0.3667, "value": -14.02}]}, "body2d": {"rotate": [{"value": -9.41, "curve": "stepped"}, {"time": 0.0667, "value": -9.41, "curve": [0.1, -9.41, 0.133, 28.77]}, {"time": 0.1667, "value": 28.77, "curve": [0.211, 28.77, 0.256, 4.09]}, {"time": 0.3, "value": -8.25}]}, "big_leg8": {"rotate": [{"value": -17.24, "curve": "stepped"}, {"time": 0.1, "value": -17.24, "curve": [0.133, -17.24, 0.167, 45.61]}, {"time": 0.2, "value": 45.61, "curve": [0.233, 45.61, 0.267, -7.37]}, {"time": 0.3, "value": -33.85}]}, "big_leg7": {"rotate": [{"value": -17.55, "curve": "stepped"}, {"time": 0.0667, "value": -17.55, "curve": [0.1, -17.55, 0.167, 45.3]}, {"time": 0.2, "value": 45.3, "curve": [0.233, 45.3, 0.3, -7.67]}, {"time": 0.3333, "value": -34.16}]}, "big_leg6": {"rotate": [{"value": -12.1, "curve": "stepped"}, {"time": 0.0333, "value": -12.1, "curve": [0.067, -12.1, 0.133, 50.75]}, {"time": 0.1667, "value": 50.75, "curve": [0.2, 50.75, 0.267, -2.22]}, {"time": 0.3, "value": -28.71}]}, "big_leg5": {"rotate": [{"value": -23.26, "curve": [0.09, -27.53, 0.1, 39.59]}, {"time": 0.1333, "value": 39.59, "curve": [0.167, 39.59, 0.233, -13.38]}, {"time": 0.2667, "value": -39.86}]}, "body2d3": {"rotate": [{"value": -45.68, "curve": "stepped"}, {"time": 0.1, "value": -45.68, "curve": [0.133, -45.68, 0.167, 0.42]}, {"time": 0.2, "value": 0.42, "curve": [0.244, 0.42, 0.322, -33.02]}, {"time": 0.3667, "value": -49.74}]}, "body2c3": {"rotate": [{"value": -22.67, "curve": "stepped"}, {"time": 0.0667, "value": -22.67, "curve": [0.1, -22.67, 0.133, 23.43]}, {"time": 0.1667, "value": 23.43, "curve": [0.211, 23.43, 0.289, -10.01]}, {"time": 0.3333, "value": -26.73}]}, "body2b3": {"rotate": [{"value": -14.8, "curve": "stepped"}, {"time": 0.0333, "value": -14.8, "curve": [0.067, -14.8, 0.1, 31.31]}, {"time": 0.1333, "value": 31.31, "curve": [0.178, 31.31, 0.256, -2.14]}, {"time": 0.3, "value": -18.86}]}, "body9": {"rotate": [{"value": -20.79, "curve": [0.112, -22.13, 0.067, 25.31]}, {"time": 0.1, "value": 25.31, "curve": [0.144, 25.31, 0.222, -8.13]}, {"time": 0.2667, "value": -24.85}]}, "eye_L2": {"translate": [{"x": 0.16, "y": 3.81, "curve": "stepped"}, {"time": 0.1333, "x": 0.16, "y": 3.81, "curve": [0.156, 0.16, 0.244, 21.81, 0.156, 3.81, 0.244, -9.72]}, {"time": 0.2667, "x": 21.81, "y": -9.72}], "scale": [{"curve": [0.089, 1, 0.178, 1.656, 0.089, 1, 0.178, 1.656]}, {"time": 0.2667, "x": 1.656, "y": 1.656}]}, "eye_L3": {"translatex": [{"value": -2.6}, {"time": 0.2667, "value": -4.9}], "translatey": [{"value": 7.01, "curve": [0.056, 7.01, 0.178, 13.2]}, {"time": 0.2667, "value": 13.2}], "scale": [{"x": 1.081, "curve": [0.089, 1.081, 0.178, 0.126, 0.089, 1, 0.178, 1]}, {"time": 0.2667, "x": 0.126}]}, "ik_rigth3": {"translatex": [{"value": -0.83}], "translatey": [{"value": -5.29}, {"time": 0.2667, "value": -9.53}], "scale": [{"x": 1.111, "curve": [0.089, 1.111, 0.178, 0.28, 0.089, 1, 0.178, 1]}, {"time": 0.2667, "x": 0.28}]}, "ik_rigth4": {"translate": [{"x": 0.18, "y": -3.81, "curve": "stepped"}, {"time": 0.1333, "x": 0.18, "y": -3.81, "curve": [0.167, 0.18, 0.233, -12.43, 0.167, -3.81, 0.233, 21.47]}, {"time": 0.2667, "x": -12.43, "y": 21.47}], "scale": [{"curve": [0.089, 1, 0.178, 1.949, 0.089, 1, 0.178, 1.949]}, {"time": 0.2667, "x": 1.949, "y": 1.949}]}, "eye_L4": {"translate": [{"x": 15.13, "y": -38.75}, {"time": 0.2667, "x": 17.16, "y": -43.97}], "scale": [{"x": 1.02}]}, "ik_rigth5": {"translate": [{"x": -0.37, "y": 28.68}, {"time": 0.2667, "x": -1.18, "y": 37.83}], "scale": [{"x": 1.236, "curve": [0.111, 1.236, 0.178, 0.921, 0.111, 1, 0.178, 1]}, {"time": 0.2667, "x": 0.921}]}, "ik_left": {"rotate": [{"curve": [0.011, 1.81, 0.022, 5.43]}, {"time": 0.0333, "value": 5.43, "curve": [0.067, 5.43, 0.1, -39.26]}, {"time": 0.1333, "value": -39.26, "curve": [0.167, -39.26, 0.2, 13.19]}, {"time": 0.2333, "value": 13.19, "curve": [0.256, 13.19, 0.278, 7.43]}, {"time": 0.3, "value": 4.55}], "translate": [{"curve": [0.056, 0, 0.044, -23.35, 0.056, 0, 0.044, -13.98]}, {"time": 0.0667, "x": -31.71, "y": -13.98, "curve": [0.078, -35.89, 0.089, -37.62, 0.078, -13.98, 0.089, 15.04]}, {"time": 0.1, "x": -37.62, "y": 15.04, "curve": [0.133, -37.62, 0.167, 11.3, 0.133, 15.04, 0.167, -26.31]}, {"time": 0.2, "x": 23.47, "y": -26.31, "curve": [0.244, 39.71, 0.289, 39.58, 0.244, -26.31, 0.289, -12.54]}, {"time": 0.3333, "x": 47.63, "y": -5.66}], "scale": [{"curve": [0.011, 1.009, 0.022, 1.027, 0.011, 0.92, 0.022, 0.76]}, {"time": 0.0333, "x": 1.027, "y": 0.76, "curve": [0.056, 1.027, 0.078, 1, 0.056, 0.76, 0.078, 1]}, {"time": 0.1, "curve": [0.122, 1, 0.144, 1.51, 0.122, 1, 0.144, 0.891]}, {"time": 0.1667, "x": 1.51, "y": 0.891, "curve": [0.189, 1.51, 0.211, 1.446, 0.189, 0.891, 0.211, 1.543]}, {"time": 0.2333, "x": 1.413, "y": 1.711, "curve": [0.256, 1.38, 0.278, 1.346, 0.256, 1.878, 0.278, 1.834]}, {"time": 0.3, "x": 1.312, "y": 1.896}]}, "ik_rigth6": {"rotate": [{"value": 3.45}], "translate": [{"x": -11.38, "y": -25.71}]}, "eye_L": {"rotate": [{"curve": [0.022, 0, 0.078, 8.23]}, {"time": 0.1, "value": 8.23, "curve": [0.122, 8.23, 0.178, -2.41]}, {"time": 0.2, "value": -2.41}], "translate": [{"y": 1.8}], "scale": [{"curve": [0.089, 1, 0.178, 1.096, 0.089, 1, 0.178, 1.096]}, {"time": 0.2667, "x": 1.096, "y": 1.096}]}, "ik_rigth2": {"translate": [{"y": -1.76}]}, "eye_L5": {"rotate": [{"value": 73.52}], "translate": [{"x": 32.24, "y": 28.65}]}, "mauth3": {"rotate": [{"value": -15.59, "curve": [0.078, -15.59, 0.156, 39.29]}, {"time": 0.2333, "value": 39.29}], "translate": [{"y": 5.34, "curve": [0.078, 0, 0.156, 0.12, 0.078, 5.34, 0.156, -8.18]}, {"time": 0.2333, "x": 0.12, "y": -8.18}]}, "mauth4": {"rotate": [{"value": 5.17, "curve": [0.078, 5.17, 0.156, -21.92]}, {"time": 0.2333, "value": -21.92}], "translate": [{"y": -4.53, "curve": [0.078, 0, 0.156, 3.41, 0.078, -4.53, 0.156, -3.36]}, {"time": 0.2333, "x": 3.41, "y": -3.36}]}, "mauth2": {"translate": [{"y": -1.88, "curve": [0.078, 0, 0.156, -0.97, 0.078, -1.88, 0.156, 1.14]}, {"time": 0.2333, "x": -0.97, "y": 1.14}], "scale": [{"x": 1.135, "curve": [0.078, 1.135, 0.156, 0.731, 0.078, 1, 0.156, 1]}, {"time": 0.2333, "x": 0.731}]}, "body3": {"translate": [{"x": -7.46}], "scale": [{"curve": [0.089, 1, 0.178, 1.134, 0.089, 1, 0.178, 1.134]}, {"time": 0.2667, "x": 1.134, "y": 1.134}]}, "body5": {"rotate": [{"curve": [0.089, 0, 0.244, 25.34]}, {"time": 0.3333, "value": 25.34}], "translate": [{"x": 8.21}], "scale": [{"curve": [0.089, 1, 0.211, 1.094, 0.089, 1, 0.211, 1.094]}, {"time": 0.3, "x": 1.094, "y": 1.094}]}, "body8": {"rotate": [{"curve": [0.089, 0, 0.211, 47.41]}, {"time": 0.3, "value": 47.41}], "translate": [{"x": 6.71}], "scale": [{"curve": [0.089, 1, 0.211, 1.094, 0.089, 1, 0.211, 1.094]}, {"time": 0.3, "x": 1.094, "y": 1.094}]}, "body4": {"rotate": [{"curve": [0.089, 0, 0.178, -17.36]}, {"time": 0.2667, "value": -17.36}], "translate": [{"x": -7.46}], "scale": [{"curve": [0.089, 1, 0.178, 1.449, 0.089, 1, 0.178, 1.449]}, {"time": 0.2667, "x": 1.449, "y": 1.449}]}, "blot": {"translate": [{"x": -12.21, "y": 15.4, "curve": "stepped"}, {"time": 0.3, "x": -12.21, "y": 15.4, "curve": [0.422, -12.21, 0.544, -12.22, 0.376, 7.46, 0.544, 1.44]}, {"time": 0.6667, "x": -12.22, "y": 1.44}], "scale": [{"x": 0.7, "y": 0.7}, {"time": 0.2667, "x": 0.449, "y": 0.449, "curve": [0.288, 1.12, 0.378, 1.2, 0.288, 1.12, 0.378, 1.2]}, {"time": 0.4333, "x": 1.2, "y": 1.2}]}, "blot_drops_control": {"translate": [{}, {"time": 0.3333, "x": -0.54, "y": -0.54, "curve": [0.556, -0.54, 0.778, 0, 0.479, -0.67, 0.778, -16.59]}, {"time": 1, "y": -16.59}]}, "blot_drop2": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": [0.333, 0, 0.4, -67.84]}, {"time": 0.4667, "value": -67.84}], "translate": [{}, {"time": 0.2667, "x": 41.44, "y": 7.91, "curve": [0.331, 77.65, 0.422, 273.93, 0.327, -38.99, 0.435, -211.23]}, {"time": 0.5, "x": 273.93, "y": -398.86}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop3": {"rotate": [{}, {"time": 0.2667, "value": -35.08, "curve": [0.329, 32.76, 0.489, 77.81]}, {"time": 0.6, "value": 77.81}], "translate": [{}, {"time": 0.2667, "x": -74.68, "y": 24.95, "curve": [0.352, -115.07, 0.489, -322.02, 0.363, 261.05, 0.507, -337.68]}, {"time": 0.6, "x": -322.02, "y": -605.72}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": [0.511, 1, 0.556, 0.4, 0.511, 1, 0.556, 0.4]}, {"time": 0.6, "x": 0.4, "y": 0.4}]}, "blot_drop4": {"rotate": [{}, {"time": 0.2667, "value": 16.41, "curve": [0.363, 68.37, 0.467, 77.81]}, {"time": 0.5667, "value": 77.81}], "translate": [{}, {"time": 0.2667, "x": -48.78, "y": -9.58, "curve": [0.321, -164.38, 0.467, -211.51, 0.344, -53.5, 0.495, -476.89]}, {"time": 0.5667, "x": -211.51, "y": -781.84}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop_s1": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{}, {"time": 0.2667, "x": 54.1, "y": 60.11, "curve": [0.334, 209.62, 0.4, 276.96, 0.333, -14.18, 0.39, -99.2]}, {"time": 0.4667, "x": 276.96, "y": -195.12}]}, "blot_drop_s2": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{}, {"time": 0.2667, "x": -53.64, "y": 83.69, "curve": [0.314, -164.57, 0.409, -315.66, 0.357, 159.13, 0.426, 84.28]}, {"time": 0.5, "x": -370.66, "y": -36.32}]}, "blot_drop5": {"rotate": [{}, {"time": 0.2667, "value": 103.14, "curve": [0.356, 103.14, 0.519, 97.56]}, {"time": 0.5333, "value": 97.56}], "translate": [{}, {"time": 0.2667, "x": 35.48, "y": -53.58, "curve": [0.355, 58.13, 0.467, 37.99, 0.344, -113.89, 0.483, -373.65]}, {"time": 0.5667, "x": 37.99, "y": -614.89}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop_s3": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{}, {"time": 0.2667, "x": 10.55, "y": -19.73, "curve": [0.323, -6.01, 0.37, -21.01, 0.314, -79.55, 0.361, -201.42]}, {"time": 0.4333, "x": -31.65, "y": -390.13}]}, "blot_drop6": {"rotate": [{}, {"time": 0.2667, "value": -75.4, "curve": [0.309, -120.98, 0.393, -263.98]}, {"time": 0.5, "value": -261.68}], "translate": [{}, {"time": 0.2667, "x": 9.31, "y": 91.31, "curve": [0.358, 118.46, 0.511, 297.6, 0.322, 320.53, 0.481, 364.92]}, {"time": 0.6333, "x": 297.6, "y": -1347.33}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": [0.544, 1, 0.589, 0.4, 0.544, 1, 0.589, 0.4]}, {"time": 0.6333, "x": 0.4, "y": 0.4}]}, "blot_drop_s4": {"rotate": [{"curve": "stepped"}, {"time": 0.3}], "translate": [{}, {"time": 0.3, "x": -53.64, "y": 83.69, "curve": [0.388, -170.51, 0.54, -244.31, 0.46, -107.5, 0.576, -316.02]}, {"time": 0.6667, "x": -277.78, "y": -598.88}]}, "ik_rigth": {"rotate": [{"curve": [0.033, 8.86, 0.067, 26.59]}, {"time": 0.1, "value": 26.59, "curve": [0.122, 26.59, 0.144, 21.4]}, {"time": 0.1667, "value": 13.15, "curve": [0.189, 4.91, 0.211, -22.88]}, {"time": 0.2333, "value": -22.88, "curve": [0.256, -22.88, 0.278, -18.49]}, {"time": 0.3, "value": -16.29}], "translate": [{"curve": [0.022, 5.4, 0.044, 11.11, 0.022, 7.83, 0.044, 23.5]}, {"time": 0.0667, "x": 16.2, "y": 23.5, "curve": [0.089, 21.29, 0.111, 30.52, 0.089, 23.5, 0.111, -13.3]}, {"time": 0.1333, "x": 30.52, "y": -26.25, "curve": [0.156, 30.52, 0.178, 17.1, 0.156, -39.21, 0.178, -54.24]}, {"time": 0.2, "x": 7.86, "y": -54.24, "curve": [0.233, -6.01, 0.267, -23.25, 0.233, -54.24, 0.267, -2.23]}, {"time": 0.3, "x": -38.81, "y": 23.78}], "scale": [{"curve": [0.022, 1.031, 0.044, 1.092, 0.022, 0.893, 0.044, 0.679]}, {"time": 0.0667, "x": 1.092, "y": 0.679, "curve": [0.089, 1.092, 0.111, 0.807, 0.089, 0.679, 0.111, 0.807]}, {"time": 0.1333, "x": 0.807, "y": 0.807, "curve": [0.156, 0.807, 0.178, 1.052, 0.156, 0.807, 0.178, 0.718]}, {"time": 0.2, "x": 1.146, "y": 0.718, "curve": [0.211, 1.193, 0.222, 1.214, 0.211, 0.718, 0.222, 1.433]}, {"time": 0.2333, "x": 1.231, "y": 1.502, "curve": [0.256, 1.263, 0.278, 1.271, 0.256, 1.64, 0.278, 1.594]}, {"time": 0.3, "x": 1.292, "y": 1.64}]}, "body": {"rotate": [{}], "scale": [{"curve": [0.033, 0.995, 0.067, 1, 0.033, 0.939, 0.067, 0.817]}, {"time": 0.1, "x": 0.986, "y": 0.817, "curve": [0.167, 0.957, 0.233, 0.788, 0.167, 0.817, 0.233, 1.322]}, {"time": 0.3, "x": 0.689, "y": 1.575}]}, "mauth": {"rotate": [{"curve": [0.022, 1.58, 0.044, 4.73]}, {"time": 0.0667, "value": 4.73, "curve": [0.089, 4.73, 0.111, -2.66]}, {"time": 0.1333, "value": -6.36}], "scale": [{"curve": [0.011, 1.038, 0.022, 1.063, 0.011, 0.943, 0.022, 0.855]}, {"time": 0.0333, "x": 1.115, "y": 0.83, "curve": [0.056, 1.217, 0.078, 1.46, 0.056, 0.779, 0.078, 0.807]}, {"time": 0.1, "x": 1.46, "y": 0.77, "curve": [0.122, 1.46, 0.144, 1.22, 0.122, 0.732, 0.144, 0.608]}, {"time": 0.1667, "x": 1.153, "y": 0.608, "curve": [0.2, 1.051, 0.233, 1.019, 0.2, 0.608, 0.233, 1.016]}, {"time": 0.2667, "x": 0.953, "y": 1.22}]}, "death": {"translate": [{"y": 20.58}]}}}, "t1_IDLE1": {"bones": {"body_cntrl": {"translatex": [{"value": -2.66, "curve": [0.013, -2.69, 0.023, -2.77]}, {"time": 0.0333, "value": -2.77, "curve": [0.256, -2.77, 0.478, 5.25]}, {"time": 0.7, "value": 5.25, "curve": [0.922, 5.25, 1.144, -2.77]}, {"time": 1.3667, "value": -2.77, "curve": [1.589, -2.77, 1.811, 5.25]}, {"time": 2.0333, "value": 5.25, "curve": [2.245, 5.25, 2.457, -1.98]}, {"time": 2.6667, "value": -2.66}], "translatey": [{"curve": [0.06, 3.57, 0.182, 13.3]}, {"time": 0.3, "value": 14.22, "curve": [0.322, 14.39, 0.344, 14.49]}, {"time": 0.3667, "value": 14.49, "curve": [0.467, 14.49, 0.607, 3.64]}, {"time": 0.6667, "curve": [0.727, 3.57, 0.849, 15.34]}, {"time": 0.9667, "value": 16.27, "curve": [0.989, 16.44, 1.011, 16.58]}, {"time": 1.0333, "value": 16.58, "curve": [1.133, 16.58, 1.233, -5.96]}, {"time": 1.3333, "curve": [1.393, 3.57, 1.516, 13.3]}, {"time": 1.6333, "value": 14.22, "curve": [1.656, 14.39, 1.678, 14.49]}, {"time": 1.7, "value": 14.49, "curve": [1.8, 14.49, 1.94, 3.64]}, {"time": 2, "curve": [2.06, 3.57, 2.182, 11.97]}, {"time": 2.3, "value": 12.9, "curve": [2.322, 13.07, 2.344, 13.14]}, {"time": 2.3667, "value": 13.14, "curve": [2.467, 13.14, 2.607, 3.64]}, {"time": 2.6667}], "scale": [{"x": 0.99, "y": 1.01, "curve": [0.022, 0.995, 0.044, 1, 0.022, 1.005, 0.044, 1]}, {"time": 0.0667, "curve": [0.111, 1, 0.156, 0.98, 0.111, 1, 0.156, 1.02]}, {"time": 0.2, "x": 0.98, "y": 1.02, "curve": "stepped"}, {"time": 0.2667, "x": 0.98, "y": 1.02, "curve": [0.311, 0.98, 0.356, 1, 0.311, 1.02, 0.356, 1]}, {"time": 0.4, "curve": [0.444, 1, 0.489, 0.98, 0.444, 1, 0.489, 1.02]}, {"time": 0.5333, "x": 0.98, "y": 1.02, "curve": "stepped"}, {"time": 0.6, "x": 0.98, "y": 1.02, "curve": [0.644, 0.98, 0.689, 1.02, 0.644, 1.02, 0.689, 0.98]}, {"time": 0.7333, "x": 1.02, "y": 0.98, "curve": [0.778, 1.02, 0.822, 0.98, 0.778, 0.98, 0.822, 1.02]}, {"time": 0.8667, "x": 0.98, "y": 1.02, "curve": "stepped"}, {"time": 0.9333, "x": 0.98, "y": 1.02, "curve": [0.978, 0.98, 1.022, 1, 0.978, 1.02, 1.022, 1]}, {"time": 1.0667, "curve": [1.111, 1, 1.156, 0.98, 1.111, 1, 1.156, 1.02]}, {"time": 1.2, "x": 0.98, "y": 1.02, "curve": "stepped"}, {"time": 1.2667, "x": 0.98, "y": 1.02, "curve": [1.311, 0.98, 1.356, 1.02, 1.311, 1.02, 1.356, 0.98]}, {"time": 1.4, "x": 1.02, "y": 0.98, "curve": [1.444, 1.02, 1.489, 0.98, 1.444, 0.98, 1.489, 1.02]}, {"time": 1.5333, "x": 0.98, "y": 1.02, "curve": "stepped"}, {"time": 1.6, "x": 0.98, "y": 1.02, "curve": [1.644, 0.98, 1.689, 1, 1.644, 1.02, 1.689, 1]}, {"time": 1.7333, "curve": [1.778, 1, 1.822, 0.98, 1.778, 1, 1.822, 1.02]}, {"time": 1.8667, "x": 0.98, "y": 1.02, "curve": "stepped"}, {"time": 1.9333, "x": 0.98, "y": 1.02, "curve": [1.978, 0.98, 2.022, 1.02, 1.978, 1.02, 2.022, 0.98]}, {"time": 2.0667, "x": 1.02, "y": 0.98, "curve": [2.111, 1.02, 2.156, 0.98, 2.111, 0.98, 2.156, 1.02]}, {"time": 2.2, "x": 0.98, "y": 1.02, "curve": "stepped"}, {"time": 2.2667, "x": 0.98, "y": 1.02, "curve": [2.311, 0.98, 2.356, 1, 2.311, 1.02, 2.356, 1]}, {"time": 2.4, "curve": [2.444, 1, 2.489, 0.98, 2.444, 1, 2.489, 1.02]}, {"time": 2.5333, "x": 0.98, "y": 1.02, "curve": "stepped"}, {"time": 2.6, "x": 0.98, "y": 1.02, "curve": [2.622, 0.98, 2.644, 0.985, 2.622, 1.02, 2.644, 1.015]}, {"time": 2.6667, "x": 0.99, "y": 1.01}]}, "big_leg": {"rotate": [{"value": -9.28, "curve": [0.057, -11.44, 0.112, -13.05]}, {"time": 0.1667, "value": -13.05, "curve": [0.389, -13.05, 0.611, 10.52]}, {"time": 0.8333, "value": 10.52, "curve": [1.001, 10.52, 1.167, -2.96]}, {"time": 1.3333, "value": -9.28, "curve": [1.39, -11.44, 1.445, -13.05]}, {"time": 1.5, "value": -13.05, "curve": [1.722, -13.05, 1.944, 10.52]}, {"time": 2.1667, "value": 10.52, "curve": [2.334, 10.52, 2.501, -2.68]}, {"time": 2.6667, "value": -9.28}]}, "big_leg2": {"rotate": [{"value": -1.56, "curve": [0.113, -7.26, 0.223, -19.49]}, {"time": 0.3333, "value": -19.49, "curve": [0.556, -19.49, 0.778, 16.37]}, {"time": 1, "value": 16.37, "curve": [1.112, 16.37, 1.222, 4.07]}, {"time": 1.3333, "value": -1.56, "curve": [1.446, -7.26, 1.556, -19.49]}, {"time": 1.6667, "value": -19.49, "curve": [1.889, -19.49, 2.111, 16.37]}, {"time": 2.3333, "value": 16.37, "curve": [2.445, 16.37, 2.557, 4.22]}, {"time": 2.6667, "value": -1.56}]}, "big_leg3": {"rotate": [{"value": 6.25, "curve": [0.168, -0.24, 0.334, -23.88]}, {"time": 0.5, "value": -23.88, "curve": [0.722, -23.88, 0.944, 11.99]}, {"time": 1.1667, "value": 11.99, "curve": [1.223, 11.99, 1.278, 8.4]}, {"time": 1.3333, "value": 6.25, "curve": [1.501, -0.24, 1.667, -23.88]}, {"time": 1.8333, "value": -23.88, "curve": [2.056, -23.88, 2.278, 11.99]}, {"time": 2.5, "value": 11.99, "curve": [2.556, 11.99, 2.613, 8.45]}, {"time": 2.6667, "value": 6.25}]}, "big_leg4": {"rotate": [{"value": 9.93, "curve": [0.222, 9.93, 0.444, -25.94]}, {"time": 0.6667, "value": -25.94, "curve": [0.847, -25.94, 1.111, 9.93]}, {"time": 1.3333, "value": 9.93, "curve": [1.556, 9.93, 1.778, -25.94]}, {"time": 2, "value": -25.94, "curve": [2.18, -25.94, 2.444, 9.93]}, {"time": 2.6667, "value": 9.93}]}, "body2": {"rotate": [{"value": -2.43, "curve": [0.079, -4.09, 0.156, -5.44]}, {"time": 0.2333, "value": -5.44, "curve": [0.456, -5.44, 0.678, 5.15]}, {"time": 0.9, "value": 5.15, "curve": [1.045, 5.15, 1.189, 0.6]}, {"time": 1.3333, "value": -2.43, "curve": [1.413, -4.09, 1.49, -5.44]}, {"time": 1.5667, "value": -5.44, "curve": [1.789, -5.44, 2.011, 5.15]}, {"time": 2.2333, "value": 5.15, "curve": [2.379, 5.15, 2.524, 0.7]}, {"time": 2.6667, "value": -2.43}]}, "body2b": {"rotate": [{"value": 4.79, "curve": [0.146, -2.93, 0.29, -13.93]}, {"time": 0.4333, "value": -13.93, "curve": [0.656, -13.93, 0.878, 12.21]}, {"time": 1.1, "value": 12.21, "curve": [1.179, 12.21, 1.256, 8.91]}, {"time": 1.3333, "value": 4.79, "curve": [1.479, -2.93, 1.623, -13.93]}, {"time": 1.7667, "value": -13.93, "curve": [1.989, -13.93, 2.211, 12.21]}, {"time": 2.4333, "value": 12.21, "curve": [2.512, 12.21, 2.59, 9.01]}, {"time": 2.6667, "value": 4.79}]}, "body2c": {"rotate": [{"value": 18.21, "curve": [0.213, 14.59, 0.423, -19.11]}, {"time": 0.6333, "value": -19.11, "curve": [0.856, -19.11, 1.078, 18.73]}, {"time": 1.3, "value": 18.73, "curve": [1.312, 18.73, 1.322, 18.39]}, {"time": 1.3333, "value": 18.21, "curve": [1.546, 14.59, 1.756, -19.11]}, {"time": 1.9667, "value": -19.11, "curve": [2.189, -19.11, 2.411, 18.73]}, {"time": 2.6333, "value": 18.73, "curve": [2.645, 18.73, 2.657, 18.41]}, {"time": 2.6667, "value": 18.21}]}, "body2d": {"rotate": [{"value": 10.51, "curve": [0.079, 17.93, 0.156, 23.92]}, {"time": 0.2333, "value": 23.92, "curve": [0.456, 23.92, 0.678, -23.31]}, {"time": 0.9, "value": -23.31, "curve": [1.045, -23.31, 1.189, -3.01]}, {"time": 1.3333, "value": 10.51, "curve": [1.413, 17.93, 1.49, 23.92]}, {"time": 1.5667, "value": 23.92, "curve": [1.789, 23.92, 2.011, -23.31]}, {"time": 2.2333, "value": -23.31, "curve": [2.379, -23.31, 2.524, -3.47]}, {"time": 2.6667, "value": 10.51}]}, "big_leg8": {"rotate": [{"value": 9.93, "curve": [0.222, 9.93, 0.444, -25.94]}, {"time": 0.6667, "value": -25.94, "curve": [0.847, -25.94, 1.111, 9.93]}, {"time": 1.3333, "value": 9.93, "curve": [1.556, 9.93, 1.778, -25.94]}, {"time": 2, "value": -25.94, "curve": [2.18, -25.94, 2.444, 9.93]}, {"time": 2.6667, "value": 9.93}]}, "big_leg7": {"rotate": [{"value": 9.62, "curve": [0.035, 10.94, 0.067, 11.99]}, {"time": 0.1, "value": 11.99, "curve": [0.156, 11.99, 0.213, 8.45]}, {"time": 0.2667, "value": 6.25, "curve": [0.435, -0.24, 0.601, -23.88]}, {"time": 0.7667, "value": -23.88, "curve": [0.956, -23.88, 1.144, 2.48]}, {"time": 1.3333, "value": 9.62, "curve": [1.368, 10.94, 1.401, 11.99]}, {"time": 1.4333, "value": 11.99, "curve": [1.49, 11.99, 1.546, 8.45]}, {"time": 1.6, "value": 6.25, "curve": [1.768, -0.24, 1.934, -23.88]}, {"time": 2.1, "value": -23.88, "curve": [2.29, -23.88, 2.479, 1.96]}, {"time": 2.6667, "value": 9.62}]}, "big_leg6": {"rotate": [{"value": 15.08, "curve": [0.09, 11.78, 0.179, 3.07]}, {"time": 0.2667, "value": -1.56, "curve": [0.379, -7.26, 0.49, -19.49]}, {"time": 0.6, "value": -19.49, "curve": [0.822, -19.49, 1.044, 16.37]}, {"time": 1.2667, "value": 16.37, "curve": [1.289, 16.37, 1.311, 15.89]}, {"time": 1.3333, "value": 15.08, "curve": [1.423, 11.78, 1.512, 3.07]}, {"time": 1.6, "value": -1.56, "curve": [1.713, -7.26, 1.823, -19.49]}, {"time": 1.9333, "value": -19.49, "curve": [2.156, -19.49, 2.378, 16.37]}, {"time": 2.6, "value": 16.37, "curve": [2.622, 16.37, 2.645, 15.9]}, {"time": 2.6667, "value": 15.08}]}, "big_leg5": {"rotate": [{"value": 3.92, "curve": [0.09, -0.35, 0.179, -5.76]}, {"time": 0.2667, "value": -9.28, "curve": [0.324, -11.44, 0.379, -13.05]}, {"time": 0.4333, "value": -13.05, "curve": [0.656, -13.05, 0.878, 10.52]}, {"time": 1.1, "value": 10.52, "curve": [1.178, 10.52, 1.256, 7.61]}, {"time": 1.3333, "value": 3.92, "curve": [1.423, -0.35, 1.512, -5.76]}, {"time": 1.6, "value": -9.28, "curve": [1.657, -11.44, 1.712, -13.05]}, {"time": 1.7667, "value": -13.05, "curve": [1.989, -13.05, 2.211, 10.52]}, {"time": 2.4333, "value": 10.52, "curve": [2.511, 10.52, 2.59, 7.65]}, {"time": 2.6667, "value": 3.92}]}, "body2d3": {"rotate": [{"value": -20.39, "curve": [0.112, -14.41, 0.223, -0.25]}, {"time": 0.3333, "value": 10.51, "curve": [0.413, 17.93, 0.49, 23.92]}, {"time": 0.5667, "value": 23.92, "curve": [0.789, 23.92, 1.011, -23.31]}, {"time": 1.2333, "value": -23.31, "curve": [1.267, -23.31, 1.3, -22.18]}, {"time": 1.3333, "value": -20.39, "curve": [1.445, -14.41, 1.557, -0.25]}, {"time": 1.6667, "value": 10.51, "curve": [1.746, 17.93, 1.823, 23.92]}, {"time": 1.9, "value": 23.92, "curve": [2.122, 23.92, 2.344, -23.31]}, {"time": 2.5667, "value": -23.31, "curve": [2.6, -23.31, 2.633, -22.18]}, {"time": 2.6667, "value": -20.39}]}, "body2c3": {"rotate": [{"value": 2.61, "curve": [0.101, 10.97, 0.201, 18.73]}, {"time": 0.3, "value": 18.73, "curve": [0.312, 18.73, 0.324, 18.41]}, {"time": 0.3333, "value": 18.21, "curve": [0.546, 14.59, 0.756, -19.11]}, {"time": 0.9667, "value": -19.11, "curve": [1.09, -19.11, 1.211, -7.45]}, {"time": 1.3333, "value": 2.61, "curve": [1.435, 10.97, 1.534, 18.73]}, {"time": 1.6333, "value": 18.73, "curve": [1.645, 18.73, 1.657, 18.41]}, {"time": 1.6667, "value": 18.21, "curve": [1.879, 14.59, 2.09, -19.11]}, {"time": 2.3, "value": -19.11, "curve": [2.423, -19.11, 2.546, -7.74]}, {"time": 2.6667, "value": 2.61}]}, "body2b3": {"rotate": [{"value": 10.49, "curve": [0.035, 11.45, 0.067, 12.21]}, {"time": 0.1, "value": 12.21, "curve": [0.179, 12.21, 0.257, 9.01]}, {"time": 0.3333, "value": 4.79, "curve": [0.479, -2.93, 0.623, -13.93]}, {"time": 0.7667, "value": -13.93, "curve": [0.956, -13.93, 1.144, 5.28]}, {"time": 1.3333, "value": 10.49, "curve": [1.368, 11.45, 1.401, 12.21]}, {"time": 1.4333, "value": 12.21, "curve": [1.512, 12.21, 1.59, 9.01]}, {"time": 1.6667, "value": 4.79, "curve": [1.813, -2.93, 1.956, -13.93]}, {"time": 2.1, "value": -13.93, "curve": [2.29, -13.93, 2.479, 4.9]}, {"time": 2.6667, "value": 10.49}]}, "body9": {"rotate": [{"value": 4.49, "curve": [0.112, 3.15, 0.223, -0.02]}, {"time": 0.3333, "value": -2.43, "curve": [0.413, -4.09, 0.49, -5.44]}, {"time": 0.5667, "value": -5.44, "curve": [0.789, -5.44, 1.011, 5.15]}, {"time": 1.2333, "value": 5.15, "curve": [1.267, 5.15, 1.3, 4.89]}, {"time": 1.3333, "value": 4.49, "curve": [1.445, 3.15, 1.557, -0.02]}, {"time": 1.6667, "value": -2.43, "curve": [1.746, -4.09, 1.823, -5.44]}, {"time": 1.9, "value": -5.44, "curve": [2.122, -5.44, 2.344, 5.15]}, {"time": 2.5667, "value": 5.15, "curve": [2.6, 5.15, 2.633, 4.9]}, {"time": 2.6667, "value": 4.49}]}, "eye_L2": {"translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": [0.356, 0, 0.378, 5.08, 0.356, 0, 0.378, -0.22]}, {"time": 0.4, "x": 5.08, "y": -0.22, "curve": "stepped"}, {"time": 1, "x": 5.08, "y": -0.22, "curve": [1.2, 5.08, 1.044, 8.45, 1.2, -0.22, 1.044, 2.18]}, {"time": 1.0667, "x": 8.45, "y": 2.18, "curve": "stepped"}, {"time": 1.6, "x": 8.45, "y": 2.18, "curve": [1.609, 8.45, 1.619, 10.22, 1.609, 2.18, 1.619, 1.32]}, {"time": 1.6667, "x": 12.34, "y": 0.29, "curve": "stepped"}, {"time": 2.1333, "x": 12.34, "y": 0.29}, {"time": 2.2}]}, "eye_L3": {"translatex": [{"value": 1.15, "curve": [0.022, 1.15, 0.044, -0.43]}, {"time": 0.0667, "value": -0.43, "curve": [0.089, -0.43, 0.111, 0]}, {"time": 0.1333, "curve": [0.167, 0, 0.167, -0.83]}, {"time": 0.2, "value": -0.83, "curve": [0.222, -0.83, 0.244, 0]}, {"time": 0.2667, "curve": [0.289, 0, 0.311, -0.43]}, {"time": 0.3333, "value": -0.43, "curve": [0.356, -0.43, 0.378, 0]}, {"time": 0.4, "curve": [0.433, 0, 0.433, -0.83]}, {"time": 0.4667, "value": -0.83, "curve": [0.489, -0.83, 0.511, 0]}, {"time": 0.5333, "curve": [0.556, 0, 0.578, -0.43]}, {"time": 0.6, "value": -0.43, "curve": [0.622, -0.43, 0.644, 0]}, {"time": 0.6667, "curve": [0.7, 0, 0.7, -0.83]}, {"time": 0.7333, "value": -0.83, "curve": [0.756, -0.83, 0.778, 0]}, {"time": 0.8, "curve": [0.822, 0, 0.844, -0.43]}, {"time": 0.8667, "value": -0.43, "curve": [0.889, -0.43, 0.911, 0]}, {"time": 0.9333, "curve": [0.967, 0, 0.967, -0.83]}, {"time": 1, "value": -0.83, "curve": [1.022, -0.83, 1.044, 0]}, {"time": 1.0667, "curve": [1.089, 0, 1.111, -0.43]}, {"time": 1.1333, "value": -0.43, "curve": [1.156, -0.43, 1.178, 0]}, {"time": 1.2, "curve": [1.233, 0, 1.233, -0.83]}, {"time": 1.2667, "value": -0.83, "curve": [1.289, -0.83, 1.311, 0]}, {"time": 1.3333, "curve": [1.356, 0, 1.378, -0.43]}, {"time": 1.4, "value": -0.43, "curve": [1.422, -0.43, 1.444, 0]}, {"time": 1.4667, "curve": [1.5, 0, 1.5, -0.83]}, {"time": 1.5333, "value": -0.83, "curve": [1.556, -0.83, 1.578, 0]}, {"time": 1.6, "curve": [1.622, 0, 1.644, -0.43]}, {"time": 1.6667, "value": -0.43, "curve": [1.689, -0.43, 1.711, 0]}, {"time": 1.7333, "curve": [1.767, 0, 1.767, -0.83]}, {"time": 1.8, "value": -0.83, "curve": [1.822, -0.83, 1.844, 0]}, {"time": 1.8667, "curve": [1.889, 0, 1.911, -0.43]}, {"time": 1.9333, "value": -0.43, "curve": [1.956, -0.43, 1.978, 0]}, {"time": 2, "curve": [2.033, 0, 2.033, -0.83]}, {"time": 2.0667, "value": -0.83, "curve": [2.089, -0.83, 2.111, 0]}, {"time": 2.1333, "curve": [2.156, 0, 2.178, -0.43]}, {"time": 2.2, "value": -0.43, "curve": [2.222, -0.43, 2.244, 0]}, {"time": 2.2667, "curve": [2.3, 0, 2.3, -0.83]}, {"time": 2.3333, "value": -0.83, "curve": [2.356, -0.83, 2.378, 0]}, {"time": 2.4, "curve": [2.433, 0, 2.433, -0.83]}, {"time": 2.4667, "value": -0.83, "curve": [2.489, -0.83, 2.511, -0.22]}, {"time": 2.5333, "value": -0.22, "curve": [2.556, -0.22, 2.567, -0.83]}, {"time": 2.6, "value": -0.83, "curve": [2.622, -0.83, 2.644, 0]}, {"time": 2.6667}], "translatey": [{"value": -3.09, "curve": [0.022, -3.09, 0.044, 1.15]}, {"time": 0.0667, "value": 1.15, "curve": [0.089, 1.15, 0.111, -0.2]}, {"time": 0.1333, "value": -0.2, "curve": [0.167, -0.2, 0.167, 2.24]}, {"time": 0.2, "value": 2.24, "curve": [0.222, 2.24, 0.244, 0]}, {"time": 0.2667, "curve": [0.289, 0, 0.311, 1.15]}, {"time": 0.3333, "value": 1.15, "curve": [0.356, 1.15, 0.378, 0]}, {"time": 0.4, "curve": [0.433, 0, 0.433, 2.1]}, {"time": 0.4667, "value": 2.1, "curve": [0.489, 2.1, 0.511, -0.2]}, {"time": 0.5333, "value": -0.2, "curve": [0.556, -0.2, 0.578, 1.15]}, {"time": 0.6, "value": 1.15, "curve": [0.622, 1.15, 0.644, 0]}, {"time": 0.6667, "curve": [0.7, 0, 0.7, 2.51]}, {"time": 0.7333, "value": 2.51, "curve": [0.756, 2.51, 0.778, 0]}, {"time": 0.8, "curve": [0.822, 0, 0.844, 1.15]}, {"time": 0.8667, "value": 1.15, "curve": [0.889, 1.15, 0.911, 0]}, {"time": 0.9333, "curve": [0.967, 0, 0.967, 1.99]}, {"time": 1, "value": 1.99, "curve": [1.022, 1.99, 1.044, -0.19]}, {"time": 1.0667, "value": -0.19, "curve": [1.089, -0.19, 1.111, 1.15]}, {"time": 1.1333, "value": 1.15, "curve": [1.156, 1.15, 1.178, 0]}, {"time": 1.2, "curve": [1.233, 0, 1.233, 2.24]}, {"time": 1.2667, "value": 2.24, "curve": [1.289, 2.24, 1.311, 0]}, {"time": 1.3333, "curve": [1.356, 0, 1.378, 1.15]}, {"time": 1.4, "value": 1.15, "curve": [1.422, 1.15, 1.444, -0.21]}, {"time": 1.4667, "value": -0.21, "curve": [1.5, -0.21, 1.5, 2.38]}, {"time": 1.5333, "value": 2.38, "curve": [1.556, 2.38, 1.578, 0]}, {"time": 1.6, "curve": [1.622, 0, 1.644, 1.15]}, {"time": 1.6667, "value": 1.15, "curve": [1.689, 1.15, 1.711, 0]}, {"time": 1.7333, "curve": [1.767, 0, 1.767, 2.24]}, {"time": 1.8, "value": 2.24, "curve": [1.822, 2.24, 1.844, 0]}, {"time": 1.8667, "curve": [1.889, 0, 1.911, 1.15]}, {"time": 1.9333, "value": 1.15, "curve": [1.956, 1.15, 1.978, -0.19]}, {"time": 2, "value": -0.19, "curve": [2.033, -0.19, 2.033, 2.06]}, {"time": 2.0667, "value": 2.06, "curve": [2.089, 2.06, 2.111, 0]}, {"time": 2.1333, "curve": [2.156, 0, 2.178, 1.15]}, {"time": 2.2, "value": 1.15, "curve": [2.222, 1.15, 2.244, 0]}, {"time": 2.2667, "curve": [2.3, 0, 2.3, 2.24]}, {"time": 2.3333, "value": 2.24, "curve": [2.356, 2.24, 2.378, 0]}, {"time": 2.4, "curve": [2.433, 0, 2.433, 1.9]}, {"time": 2.4667, "value": 1.9, "curve": [2.489, 1.9, 2.511, 0.58]}, {"time": 2.5333, "value": 0.58, "curve": [2.556, 0.58, 2.567, 2.24]}, {"time": 2.6, "value": 2.24, "curve": [2.622, 2.24, 2.644, 0]}, {"time": 2.6667}], "scale": [{"x": 1.061}]}, "ik_rigth3": {"translatex": [{"value": -0.83, "curve": [0.022, -0.83, 0.044, 0]}, {"time": 0.0667, "curve": [0.089, 0, 0.111, -0.43]}, {"time": 0.1333, "value": -0.43, "curve": [0.156, -0.43, 0.178, 0]}, {"time": 0.2, "curve": [0.233, 0, 0.233, -0.83]}, {"time": 0.2667, "value": -0.83, "curve": [0.289, -0.83, 0.311, 0]}, {"time": 0.3333, "curve": [0.356, 0, 0.378, -0.43]}, {"time": 0.4, "value": -0.43, "curve": [0.422, -0.43, 0.444, 0]}, {"time": 0.4667, "curve": [0.5, 0, 0.5, -0.83]}, {"time": 0.5333, "value": -0.83, "curve": [0.556, -0.83, 0.578, 0]}, {"time": 0.6, "curve": [0.622, 0, 0.644, -0.43]}, {"time": 0.6667, "value": -0.43, "curve": [0.689, -0.43, 0.711, 0]}, {"time": 0.7333, "curve": [0.767, 0, 0.767, -0.83]}, {"time": 0.8, "value": -0.83, "curve": [0.822, -0.83, 0.844, 0]}, {"time": 0.8667, "curve": [0.889, 0, 0.911, -0.43]}, {"time": 0.9333, "value": -0.43, "curve": [0.956, -0.43, 0.978, 0]}, {"time": 1, "curve": [1.033, 0, 1.033, -0.83]}, {"time": 1.0667, "value": -0.83, "curve": [1.089, -0.83, 1.111, 0]}, {"time": 1.1333, "curve": [1.156, 0, 1.178, -0.43]}, {"time": 1.2, "value": -0.43, "curve": [1.222, -0.43, 1.244, 0]}, {"time": 1.2667, "curve": [1.3, 0, 1.3, -0.83]}, {"time": 1.3333, "value": -0.83, "curve": [1.356, -0.83, 1.378, 0]}, {"time": 1.4, "curve": [1.422, 0, 1.444, -0.43]}, {"time": 1.4667, "value": -0.43, "curve": [1.489, -0.43, 1.511, 0]}, {"time": 1.5333, "curve": [1.567, 0, 1.567, -0.83]}, {"time": 1.6, "value": -0.83, "curve": [1.622, -0.83, 1.644, 0]}, {"time": 1.6667, "curve": [1.689, 0, 1.711, -0.43]}, {"time": 1.7333, "value": -0.43, "curve": [1.756, -0.43, 1.778, 0]}, {"time": 1.8, "curve": [1.833, 0, 1.833, -0.83]}, {"time": 1.8667, "value": -0.83, "curve": [1.889, -0.83, 1.911, 0]}, {"time": 1.9333, "curve": [1.956, 0, 1.978, -0.43]}, {"time": 2, "value": -0.43, "curve": [2.022, -0.43, 2.044, 0]}, {"time": 2.0667, "curve": [2.1, 0, 2.1, -0.83]}, {"time": 2.1333, "value": -0.83, "curve": [2.156, -0.83, 2.178, 0]}, {"time": 2.2, "curve": [2.222, 0, 2.244, -0.43]}, {"time": 2.2667, "value": -0.43, "curve": [2.289, -0.43, 2.311, 0]}, {"time": 2.3333, "curve": [2.367, 0, 2.367, -0.83]}, {"time": 2.4, "value": -0.83, "curve": [2.422, -0.83, 2.444, 0]}, {"time": 2.4667, "curve": [2.5, 0, 2.5, -0.83]}, {"time": 2.5333, "value": -0.83, "curve": [2.556, -0.83, 2.578, -0.22]}, {"time": 2.6, "value": -0.22, "curve": [2.622, -0.22, 2.633, -0.83]}, {"time": 2.6667, "value": -0.83}], "translatey": [{"value": 4.97, "curve": [0.022, 4.97, 0.044, 0]}, {"time": 0.0667, "curve": [0.089, 0, 0.111, 1.15]}, {"time": 0.1333, "value": 1.15, "curve": [0.156, 1.15, 0.178, -0.2]}, {"time": 0.2, "value": -0.2, "curve": [0.233, -0.2, 0.233, 2.24]}, {"time": 0.2667, "value": 2.24, "curve": [0.289, 2.24, 0.311, 0]}, {"time": 0.3333, "curve": [0.356, 0, 0.378, 1.15]}, {"time": 0.4, "value": 1.15, "curve": [0.422, 1.15, 0.444, 0]}, {"time": 0.4667, "curve": [0.5, 0, 0.5, 2.1]}, {"time": 0.5333, "value": 2.1, "curve": [0.556, 2.1, 0.578, -0.2]}, {"time": 0.6, "value": -0.2, "curve": [0.622, -0.2, 0.644, 1.15]}, {"time": 0.6667, "value": 1.15, "curve": [0.689, 1.15, 0.711, 0]}, {"time": 0.7333, "curve": [0.767, 0, 0.767, 2.51]}, {"time": 0.8, "value": 2.51, "curve": [0.822, 2.51, 0.844, 0]}, {"time": 0.8667, "curve": [0.889, 0, 0.911, 1.15]}, {"time": 0.9333, "value": 1.15, "curve": [0.956, 1.15, 0.978, 0]}, {"time": 1, "curve": [1.033, 0, 1.033, 1.99]}, {"time": 1.0667, "value": 1.99, "curve": [1.089, 1.99, 1.111, -0.19]}, {"time": 1.1333, "value": -0.19, "curve": [1.156, -0.19, 1.178, 1.15]}, {"time": 1.2, "value": 1.15, "curve": [1.222, 1.15, 1.244, 0]}, {"time": 1.2667, "curve": [1.3, 0, 1.3, 2.24]}, {"time": 1.3333, "value": 2.24, "curve": [1.356, 2.24, 1.378, 0]}, {"time": 1.4, "curve": [1.422, 0, 1.444, 1.15]}, {"time": 1.4667, "value": 1.15, "curve": [1.489, 1.15, 1.511, -0.21]}, {"time": 1.5333, "value": -0.21, "curve": [1.567, -0.21, 1.567, 2.38]}, {"time": 1.6, "value": 2.38, "curve": [1.622, 2.38, 1.644, 0]}, {"time": 1.6667, "curve": [1.689, 0, 1.711, 1.15]}, {"time": 1.7333, "value": 1.15, "curve": [1.756, 1.15, 1.778, 0]}, {"time": 1.8, "curve": [1.833, 0, 1.833, 2.24]}, {"time": 1.8667, "value": 2.24, "curve": [1.889, 2.24, 1.911, 0]}, {"time": 1.9333, "curve": [1.956, 0, 1.978, 1.15]}, {"time": 2, "value": 1.15, "curve": [2.022, 1.15, 2.044, -0.19]}, {"time": 2.0667, "value": -0.19, "curve": [2.1, -0.19, 2.1, 2.06]}, {"time": 2.1333, "value": 2.06, "curve": [2.156, 2.06, 2.178, 0]}, {"time": 2.2, "curve": [2.222, 0, 2.244, 1.15]}, {"time": 2.2667, "value": 1.15, "curve": [2.289, 1.15, 2.311, 0]}, {"time": 2.3333, "curve": [2.367, 0, 2.367, 2.24]}, {"time": 2.4, "value": 2.24, "curve": [2.422, 2.24, 2.444, 0]}, {"time": 2.4667, "curve": [2.5, 0, 2.5, 1.9]}, {"time": 2.5333, "value": 1.9, "curve": [2.556, 1.9, 2.578, 0.58]}, {"time": 2.6, "value": 0.58, "curve": [2.622, 0.58, 2.633, 2.24]}, {"time": 2.6667, "value": 2.24}], "scale": [{"x": 1.103}]}, "ik_rigth4": {"translate": [{"curve": "stepped"}, {"time": 0.4667}, {"time": 0.5, "x": 3.12, "y": 5.71, "curve": "stepped"}, {"time": 1.1333, "x": 3.12, "y": 5.71}, {"time": 1.2, "x": 7.94, "y": 8.64, "curve": "stepped"}, {"time": 1.7333, "x": 7.94, "y": 8.64}, {"time": 1.7667, "x": 6.2, "y": -5.35, "curve": "stepped"}, {"time": 2.2, "x": 6.2, "y": -5.35}, {"time": 2.2667}]}, "eye_L4": {"translate": [{"x": 16.2, "y": -41.51, "curve": "stepped"}, {"time": 0.2, "x": 16.2, "y": -41.51, "curve": [0.244, 16.2, 0.289, -0.96, 0.244, -41.51, 0.289, 2.45]}, {"time": 0.3333, "x": -0.96, "y": 2.45, "curve": "stepped"}, {"time": 0.3667, "x": -0.96, "y": 2.45, "curve": [0.411, -0.96, 0.456, 16.2, 0.411, 2.45, 0.456, -41.51]}, {"time": 0.5, "x": 16.2, "y": -41.51, "curve": "stepped"}, {"time": 1, "x": 16.2, "y": -41.51, "curve": [1.033, 16.2, 1.067, 14.68, 1.033, -41.51, 1.067, -37.6]}, {"time": 1.1, "x": 14.68, "y": -37.6, "curve": [1.133, 14.68, 1.167, 16.2, 1.133, -37.6, 1.167, -41.51]}, {"time": 1.2, "x": 16.2, "y": -41.51, "curve": "stepped"}, {"time": 1.6, "x": 16.2, "y": -41.51, "curve": [1.633, 16.2, 1.667, 14.69, 1.633, -41.51, 1.667, -37.64]}, {"time": 1.7, "x": 14.69, "y": -37.64, "curve": [1.733, 14.69, 1.767, 16.2, 1.733, -37.64, 1.767, -41.51]}, {"time": 1.8, "x": 16.2, "y": -41.51, "curve": "stepped"}, {"time": 2.1333, "x": 16.2, "y": -41.51, "curve": [2.167, 16.2, 2.2, 14.22, 2.167, -41.51, 2.2, -36.42]}, {"time": 2.2333, "x": 14.22, "y": -36.42, "curve": [2.267, 14.22, 2.3, 16.2, 2.267, -36.42, 2.3, -41.51]}, {"time": 2.3333, "x": 16.2, "y": -41.51}], "scale": [{"x": 1.02, "curve": "stepped"}, {"time": 0.2, "x": 1.02, "curve": [0.244, 1.02, 0.289, 1, 0.244, 1, 0.289, 1]}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667, "curve": [0.411, 1, 0.456, 1.02, 0.411, 1, 0.456, 1]}, {"time": 0.5, "x": 1.02}]}, "ik_rigth5": {"translate": [{"x": -0.29, "y": 30.67, "curve": "stepped"}, {"time": 0.4, "x": -0.29, "y": 30.67}, {"time": 0.5, "x": 0.14, "y": 21.49}, {"time": 0.6, "x": -0.29, "y": 30.67, "curve": "stepped"}, {"time": 1.0667, "x": -0.29, "y": 30.67}, {"time": 1.1667, "x": 0.14, "y": 21.49}, {"time": 1.2667, "x": -0.29, "y": 30.67, "curve": "stepped"}, {"time": 1.6, "x": -0.29, "y": 30.67}, {"time": 1.7333, "x": 2.28, "y": -1.35, "curve": "stepped"}, {"time": 1.7667, "x": 2.28, "y": -1.35, "curve": [1.822, 2.28, 1.856, -0.29, 1.822, -1.35, 1.856, 30.67]}, {"time": 1.9, "x": -0.29, "y": 30.67, "curve": "stepped"}, {"time": 2.1, "x": -0.29, "y": 30.67}, {"time": 2.2, "x": 0.14, "y": 21.49}, {"time": 2.3, "x": -0.29, "y": 30.67}], "scale": [{"x": 1.236, "curve": "stepped"}, {"time": 1.6, "x": 1.236, "curve": [1.644, 1.236, 1.689, 1, 1.644, 1, 1.689, 1]}, {"time": 1.7333, "curve": "stepped"}, {"time": 1.7667, "curve": [1.822, 1, 1.844, 1.158, 1.822, 1, 1.844, 1]}, {"time": 1.9, "x": 1.236}]}, "eye_L5": {"rotate": [{"time": 1, "value": 0.83, "curve": [1.033, 0.83, 1.067, 15.43]}, {"time": 1.1, "value": 15.43, "curve": "stepped"}, {"time": 1.6, "value": 15.43}, {"time": 1.6667, "value": 10.72, "curve": "stepped"}, {"time": 2.1333, "value": 10.72}, {"time": 2.2}], "translate": [{"time": 1, "curve": [1.033, 0, 1.067, 9.81, 1.033, 0, 1.067, 1.14]}, {"time": 1.1, "x": 9.81, "y": 1.14, "curve": "stepped"}, {"time": 1.6, "x": 9.81, "y": 1.14}, {"time": 1.6667, "x": 9.61, "y": -3.54, "curve": "stepped"}, {"time": 2.1333, "x": 9.61, "y": -3.54}, {"time": 2.2}]}, "eye_L": {"translate": [{"time": 1, "curve": [1.033, 0.52, 1.067, 1.56, 1.033, -0.02, 1.067, -0.07]}, {"time": 1.1, "x": 1.56, "y": -0.07, "curve": "stepped"}, {"time": 1.6, "x": 1.56, "y": -0.07, "curve": [1.622, 1.56, 1.644, 2.25, 1.622, -0.07, 1.644, -2.05]}, {"time": 1.6667, "x": 2.25, "y": -2.05, "curve": "stepped"}, {"time": 2.1333, "x": 2.25, "y": -2.05, "curve": [2.156, 2.25, 2.178, 0.75, 2.156, -2.05, 2.178, -0.68]}, {"time": 2.2}]}, "ik_left": {"translate": [{}]}, "ik_rigth6": {"rotate": [{"time": 0.4667, "curve": [0.489, 2.46, 0.511, 7.39]}, {"time": 0.5333, "value": 7.39, "curve": "stepped"}, {"time": 1.1333, "value": 7.39, "curve": "stepped"}, {"time": 1.7333, "value": 7.39, "curve": [1.778, 7.39, 1.822, -14.36]}, {"time": 1.8667, "value": -14.36, "curve": "stepped"}, {"time": 2.2, "value": -14.36, "curve": [2.233, -14.36, 2.267, -4.79]}, {"time": 2.3}], "translate": [{"curve": "stepped"}, {"time": 0.4667, "curve": [0.489, 0, 0.511, 7.47, 0.489, 0, 0.511, 7.04]}, {"time": 0.5333, "x": 7.47, "y": 7.04, "curve": "stepped"}, {"time": 1.1333, "x": 7.47, "y": 7.04, "curve": [1.167, 7.47, 1.2, 11.25, 1.167, 7.04, 1.2, 5.55]}, {"time": 1.2333, "x": 11.25, "y": 5.55, "curve": "stepped"}, {"time": 1.7333, "x": 11.25, "y": 5.55, "curve": [1.778, 11.25, 1.822, 13.76, 1.778, 5.55, 1.822, -10.81]}, {"time": 1.8667, "x": 13.76, "y": -10.81, "curve": "stepped"}, {"time": 2.2, "x": 13.76, "y": -10.81, "curve": [2.233, 13.76, 2.267, 4.59, 2.233, -10.81, 2.267, -3.6]}, {"time": 2.3}]}, "mauth3": {"rotate": [{"value": 15.73}], "translate": [{"y": -3.74}]}}}, "t1_IDLE2": {"slots": {"eyelid_u_l": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00", "curve": [0.211, 1, 0.222, 1, 0.211, 1, 0.222, 1, 0.211, 1, 0.222, 1, 0.211, 0, 0.222, 1]}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff", "curve": [0.478, 1, 0.489, 1, 0.478, 1, 0.489, 1, 0.478, 1, 0.489, 1, 0.478, 1, 0.489, 0]}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0667, "color": "ffffff00", "curve": [1.078, 1, 1.089, 1, 1.078, 1, 1.089, 1, 1.078, 1, 1.089, 1, 1.078, 0, 1.089, 1]}, {"time": 1.1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4667, "color": "ffffffff", "curve": [1.478, 1, 1.489, 1, 1.478, 1, 1.489, 1, 1.478, 1, 1.489, 1, 1.478, 1, 1.489, 0]}, {"time": 1.5, "color": "ffffff00"}], "attachment": [{"name": "eyelid_u_l"}]}, "eyelid_u_r": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00", "curve": [0.211, 1, 0.222, 1, 0.211, 1, 0.222, 1, 0.211, 1, 0.222, 1, 0.211, 0, 0.222, 1]}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff", "curve": [0.478, 1, 0.489, 1, 0.478, 1, 0.489, 1, 0.478, 1, 0.489, 1, 0.478, 1, 0.489, 0]}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00", "curve": [1.211, 1, 1.222, 1, 1.211, 1, 1.222, 1, 1.211, 1, 1.222, 1, 1.211, 0, 1.222, 1]}, {"time": 1.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4667, "color": "ffffffff", "curve": [1.478, 1, 1.489, 1, 1.478, 1, 1.489, 1, 1.478, 1, 1.489, 1, 1.478, 1, 1.489, 0]}, {"time": 1.5, "color": "ffffff00"}]}}, "bones": {"body_cntrl": {"rotate": [{"curve": [0.078, -0.54, 0.156, -1.14]}, {"time": 0.2333, "value": -1.14, "curve": [0.344, -1.14, 0.456, 1.85]}, {"time": 0.5667, "value": 1.85, "curve": [0.767, 1.85, 0.967, -1.75]}, {"time": 1.1667, "value": -1.75, "curve": [1.256, -1.75, 1.344, 2.23]}, {"time": 1.4333, "value": 2.23, "curve": [1.644, 2.23, 1.856, -1.14]}, {"time": 2.0667, "value": -1.14, "curve": [2.2, -1.14, 2.333, 1.72]}, {"time": 2.4667, "value": 1.72, "curve": [2.533, 1.72, 2.6, 0.46]}, {"time": 2.6667}], "translatex": [{"value": -2.66, "curve": [0.013, -2.69, 0.023, -2.77]}, {"time": 0.0333, "value": -2.77, "curve": [0.256, -2.77, 0.478, 5.25]}, {"time": 0.7, "value": 5.25, "curve": [0.922, 5.25, 1.144, -2.77]}, {"time": 1.3667, "value": -2.77, "curve": [1.589, -2.77, 1.811, 5.25]}, {"time": 2.0333, "value": 5.25, "curve": [2.245, 5.25, 2.457, -1.98]}, {"time": 2.6667, "value": -2.66}], "translatey": [{"curve": [0.06, 3.57, 0.182, 13.3]}, {"time": 0.3, "value": 14.22, "curve": [0.322, 14.39, 0.344, 14.49]}, {"time": 0.3667, "value": 14.49, "curve": [0.467, 14.49, 0.607, 3.64]}, {"time": 0.6667, "curve": [0.727, 3.57, 0.849, 15.34]}, {"time": 0.9667, "value": 16.27, "curve": [0.989, 16.44, 1.011, 16.58]}, {"time": 1.0333, "value": 16.58, "curve": [1.133, 16.58, 1.273, 5.47]}, {"time": 1.3333, "curve": [1.393, 4.69, 1.516, 13.3]}, {"time": 1.6333, "value": 14.22, "curve": [1.656, 14.39, 1.678, 14.49]}, {"time": 1.7, "value": 14.49, "curve": [1.8, 14.49, 1.94, 3.64]}, {"time": 2, "curve": [2.06, 3.57, 2.182, 11.97]}, {"time": 2.3, "value": 12.9, "curve": [2.322, 13.07, 2.344, 13.14]}, {"time": 2.3667, "value": 13.14, "curve": [2.467, 13.14, 2.607, 3.64]}, {"time": 2.6667}], "scale": [{"x": 0.99, "y": 1.01, "curve": [0.022, 0.995, 0.044, 1, 0.022, 1.005, 0.044, 1]}, {"time": 0.0667, "curve": [0.111, 1, 0.156, 0.98, 0.111, 1, 0.156, 1.02]}, {"time": 0.2, "x": 0.98, "y": 1.02, "curve": "stepped"}, {"time": 0.2667, "x": 0.98, "y": 1.02, "curve": [0.311, 0.98, 0.356, 1, 0.311, 1.02, 0.356, 1]}, {"time": 0.4, "curve": [0.444, 1, 0.489, 0.98, 0.444, 1, 0.489, 1.02]}, {"time": 0.5333, "x": 0.98, "y": 1.02, "curve": "stepped"}, {"time": 0.6, "x": 0.98, "y": 1.02, "curve": [0.644, 0.98, 0.689, 1.02, 0.644, 1.02, 0.689, 0.98]}, {"time": 0.7333, "x": 1.02, "y": 0.98, "curve": [0.778, 1.02, 0.822, 0.98, 0.778, 0.98, 0.822, 1.02]}, {"time": 0.8667, "x": 0.98, "y": 1.02, "curve": "stepped"}, {"time": 0.9333, "x": 0.98, "y": 1.02, "curve": [0.978, 0.98, 1.022, 1, 0.978, 1.02, 1.022, 1]}, {"time": 1.0667, "curve": [1.111, 1, 1.156, 0.98, 1.111, 1, 1.156, 1.02]}, {"time": 1.2, "x": 0.98, "y": 1.02, "curve": "stepped"}, {"time": 1.2667, "x": 0.98, "y": 1.02, "curve": [1.311, 0.98, 1.356, 1.02, 1.311, 1.02, 1.356, 0.98]}, {"time": 1.4, "x": 1.02, "y": 0.98, "curve": [1.444, 1.02, 1.489, 0.98, 1.444, 0.98, 1.489, 1.02]}, {"time": 1.5333, "x": 0.98, "y": 1.02, "curve": "stepped"}, {"time": 1.6, "x": 0.98, "y": 1.02, "curve": [1.644, 0.98, 1.689, 1, 1.644, 1.02, 1.689, 1]}, {"time": 1.7333, "curve": [1.778, 1, 1.822, 0.98, 1.778, 1, 1.822, 1.02]}, {"time": 1.8667, "x": 0.98, "y": 1.02, "curve": "stepped"}, {"time": 1.9333, "x": 0.98, "y": 1.02, "curve": [1.978, 0.98, 2.022, 1.02, 1.978, 1.02, 2.022, 0.98]}, {"time": 2.0667, "x": 1.02, "y": 0.98, "curve": [2.111, 1.02, 2.156, 0.98, 2.111, 0.98, 2.156, 1.02]}, {"time": 2.2, "x": 0.98, "y": 1.02, "curve": "stepped"}, {"time": 2.2667, "x": 0.98, "y": 1.02, "curve": [2.311, 0.98, 2.356, 1, 2.311, 1.02, 2.356, 1]}, {"time": 2.4, "curve": [2.444, 1, 2.489, 0.98, 2.444, 1, 2.489, 1.02]}, {"time": 2.5333, "x": 0.98, "y": 1.02, "curve": "stepped"}, {"time": 2.6, "x": 0.98, "y": 1.02, "curve": [2.622, 0.98, 2.644, 0.985, 2.622, 1.02, 2.644, 1.015]}, {"time": 2.6667, "x": 0.99, "y": 1.01}]}, "big_leg": {"rotate": [{"value": -25.89, "curve": [0.057, -28.05, 0.112, -29.66]}, {"time": 0.1667, "value": -29.66, "curve": [0.389, -29.66, 0.611, -6.09]}, {"time": 0.8333, "value": -6.09, "curve": [1.001, -6.09, 1.167, -19.58]}, {"time": 1.3333, "value": -25.89, "curve": [1.39, -28.05, 1.445, -29.66]}, {"time": 1.5, "value": -29.66, "curve": [1.722, -29.66, 1.944, -6.09]}, {"time": 2.1667, "value": -6.09, "curve": [2.334, -6.09, 2.501, -19.29]}, {"time": 2.6667, "value": -25.89}]}, "big_leg2": {"rotate": [{"value": -18.17, "curve": [0.113, -23.88, 0.223, -36.1]}, {"time": 0.3333, "value": -36.1, "curve": [0.556, -36.1, 0.778, -0.24]}, {"time": 1, "value": -0.24, "curve": [1.112, -0.24, 1.222, -12.54]}, {"time": 1.3333, "value": -18.17, "curve": [1.446, -23.88, 1.556, -36.1]}, {"time": 1.6667, "value": -36.1, "curve": [1.889, -36.1, 2.111, -0.24]}, {"time": 2.3333, "value": -0.24, "curve": [2.445, -0.24, 2.557, -12.39]}, {"time": 2.6667, "value": -18.17}]}, "big_leg3": {"rotate": [{"value": -10.36, "curve": [0.168, -16.85, 0.334, -40.49]}, {"time": 0.5, "value": -40.49, "curve": [0.722, -40.49, 0.944, -4.62]}, {"time": 1.1667, "value": -4.62, "curve": [1.223, -4.62, 1.278, -8.21]}, {"time": 1.3333, "value": -10.36, "curve": [1.501, -16.85, 1.667, -40.49]}, {"time": 1.8333, "value": -40.49, "curve": [2.056, -40.49, 2.278, -4.62]}, {"time": 2.5, "value": -4.62, "curve": [2.556, -4.62, 2.613, -8.16]}, {"time": 2.6667, "value": -10.36}]}, "big_leg4": {"rotate": [{"value": -6.68, "curve": [0.222, -6.68, 0.444, -42.55]}, {"time": 0.6667, "value": -42.55, "curve": [0.847, -42.55, 1.111, -6.68]}, {"time": 1.3333, "value": -6.68, "curve": [1.556, -6.68, 1.778, -42.55]}, {"time": 2, "value": -42.55, "curve": [2.18, -42.55, 2.444, -6.68]}, {"time": 2.6667, "value": -6.68}]}, "body2": {"rotate": [{"value": -26.42, "curve": [0.079, -28.08, 0.156, -29.42]}, {"time": 0.2333, "value": -29.42, "curve": [0.456, -29.42, 0.678, -18.84]}, {"time": 0.9, "value": -18.84, "curve": [1.045, -18.84, 1.189, -23.39]}, {"time": 1.3333, "value": -26.42, "curve": [1.413, -28.08, 1.49, -29.42]}, {"time": 1.5667, "value": -29.42, "curve": [1.789, -29.42, 2.011, -18.84]}, {"time": 2.2333, "value": -18.84, "curve": [2.379, -18.84, 2.524, -23.29]}, {"time": 2.6667, "value": -26.42}]}, "body2b": {"rotate": [{"value": -25.94, "curve": [0.146, -33.66, 0.29, -44.65]}, {"time": 0.4333, "value": -44.65, "curve": [0.656, -44.65, 0.878, -18.52]}, {"time": 1.1, "value": -18.52, "curve": [1.179, -18.52, 1.256, -21.82]}, {"time": 1.3333, "value": -25.94, "curve": [1.479, -33.66, 1.623, -44.65]}, {"time": 1.7667, "value": -44.65, "curve": [1.989, -44.65, 2.211, -18.52]}, {"time": 2.4333, "value": -18.52, "curve": [2.512, -18.52, 2.59, -21.72]}, {"time": 2.6667, "value": -25.94}]}, "body2c": {"rotate": [{"value": -15.18, "curve": [0.213, -18.8, 0.423, -52.49]}, {"time": 0.6333, "value": -52.49, "curve": [0.856, -52.49, 1.078, -14.65]}, {"time": 1.3, "value": -14.65, "curve": [1.312, -14.65, 1.322, -14.99]}, {"time": 1.3333, "value": -15.18, "curve": [1.546, -18.8, 1.756, -52.49]}, {"time": 1.9667, "value": -52.49, "curve": [2.189, -52.49, 2.411, -14.65]}, {"time": 2.6333, "value": -14.65, "curve": [2.645, -14.65, 2.657, -14.98]}, {"time": 2.6667, "value": -15.18}]}, "body2d": {"rotate": [{"value": -9.41, "curve": [0.079, -1.99, 0.156, 4]}, {"time": 0.2333, "value": 4, "curve": [0.456, 4, 0.678, -43.23]}, {"time": 0.9, "value": -43.23, "curve": [1.045, -43.23, 1.189, -22.93]}, {"time": 1.3333, "value": -9.41, "curve": [1.413, -1.99, 1.49, 4]}, {"time": 1.5667, "value": 4, "curve": [1.789, 4, 2.011, -43.23]}, {"time": 2.2333, "value": -43.23, "curve": [2.379, -43.23, 2.524, -23.39]}, {"time": 2.6667, "value": -9.41}]}, "big_leg8": {"rotate": [{"value": -17.24, "curve": [0.222, -17.24, 0.444, -25.94]}, {"time": 0.6667, "value": -25.94, "curve": [0.847, -25.94, 1.111, 9.93]}, {"time": 1.3333, "value": 9.93, "curve": [1.556, 9.93, 1.778, -25.94]}, {"time": 2, "value": -25.94, "curve": [2.18, -25.94, 2.444, 9.93]}, {"time": 2.6667, "value": 9.93}]}, "big_leg7": {"rotate": [{"value": -17.55, "curve": [0.035, -16.23, 0.067, -15.18]}, {"time": 0.1, "value": -15.18, "curve": [0.156, -15.18, 0.213, -18.72]}, {"time": 0.2667, "value": -20.92, "curve": [0.435, -27.41, 0.601, -51.05]}, {"time": 0.7667, "value": -51.05, "curve": [0.956, -51.05, 1.144, -24.69]}, {"time": 1.3333, "value": -17.55, "curve": [1.368, -16.23, 1.401, -15.18]}, {"time": 1.4333, "value": -15.18, "curve": [1.49, -15.18, 1.546, -18.72]}, {"time": 1.6, "value": -20.92, "curve": [1.768, -27.41, 1.934, -51.05]}, {"time": 2.1, "value": -51.05, "curve": [2.29, -51.05, 2.479, -25.21]}, {"time": 2.6667, "value": -17.55}]}, "big_leg6": {"rotate": [{"value": -12.1, "curve": [0.09, -15.39, 0.179, -24.11]}, {"time": 0.2667, "value": -28.73, "curve": [0.379, -34.44, 0.49, -46.67]}, {"time": 0.6, "value": -46.67, "curve": [0.822, -46.67, 1.044, -10.8]}, {"time": 1.2667, "value": -10.8, "curve": [1.289, -10.8, 1.311, -11.28]}, {"time": 1.3333, "value": -12.1, "curve": [1.423, -15.39, 1.512, -24.11]}, {"time": 1.6, "value": -28.73, "curve": [1.713, -34.44, 1.823, -46.67]}, {"time": 1.9333, "value": -46.67, "curve": [2.156, -46.67, 2.378, -10.8]}, {"time": 2.6, "value": -10.8, "curve": [2.622, -10.8, 2.645, -11.27]}, {"time": 2.6667, "value": -12.1}]}, "big_leg5": {"rotate": [{"value": -23.26, "curve": [0.09, -27.53, 0.179, -32.93]}, {"time": 0.2667, "value": -36.45, "curve": [0.324, -38.61, 0.379, -40.22]}, {"time": 0.4333, "value": -40.22, "curve": [0.656, -40.22, 0.878, -16.66]}, {"time": 1.1, "value": -16.66, "curve": [1.178, -16.66, 1.256, -19.56]}, {"time": 1.3333, "value": -23.26, "curve": [1.423, -27.53, 1.512, -32.93]}, {"time": 1.6, "value": -36.45, "curve": [1.657, -38.61, 1.712, -40.22]}, {"time": 1.7667, "value": -40.22, "curve": [1.989, -40.22, 2.211, -16.66]}, {"time": 2.4333, "value": -16.66, "curve": [2.511, -16.66, 2.59, -19.52]}, {"time": 2.6667, "value": -23.26}]}, "body2d3": {"rotate": [{"value": -45.68, "curve": [0.112, -39.7, 0.223, -25.54]}, {"time": 0.3333, "value": -14.77, "curve": [0.413, -7.36, 0.49, -1.36]}, {"time": 0.5667, "value": -1.36, "curve": [0.789, -1.36, 1.011, -48.59]}, {"time": 1.2333, "value": -48.59, "curve": [1.267, -48.59, 1.3, -47.46]}, {"time": 1.3333, "value": -45.68, "curve": [1.445, -39.7, 1.557, -25.54]}, {"time": 1.6667, "value": -14.77, "curve": [1.746, -7.36, 1.823, -1.36]}, {"time": 1.9, "value": -1.36, "curve": [2.122, -1.36, 2.344, -48.59]}, {"time": 2.5667, "value": -48.59, "curve": [2.6, -48.59, 2.633, -47.47]}, {"time": 2.6667, "value": -45.68}]}, "body2c3": {"rotate": [{"value": -22.67, "curve": [0.101, -14.32, 0.201, -6.55]}, {"time": 0.3, "value": -6.55, "curve": [0.312, -6.55, 0.324, -6.88]}, {"time": 0.3333, "value": -7.08, "curve": [0.546, -10.7, 0.756, -44.39]}, {"time": 0.9667, "value": -44.39, "curve": [1.09, -44.39, 1.211, -32.74]}, {"time": 1.3333, "value": -22.67, "curve": [1.435, -14.32, 1.534, -6.55]}, {"time": 1.6333, "value": -6.55, "curve": [1.645, -6.55, 1.657, -6.88]}, {"time": 1.6667, "value": -7.08, "curve": [1.879, -10.7, 2.09, -44.39]}, {"time": 2.3, "value": -44.39, "curve": [2.423, -44.39, 2.546, -33.02]}, {"time": 2.6667, "value": -22.67}]}, "body2b3": {"rotate": [{"value": -14.8, "curve": [0.035, -13.84, 0.067, -13.07]}, {"time": 0.1, "value": -13.07, "curve": [0.179, -13.07, 0.257, -16.28]}, {"time": 0.3333, "value": -20.5, "curve": [0.479, -28.22, 0.623, -39.21]}, {"time": 0.7667, "value": -39.21, "curve": [0.956, -39.21, 1.144, -20]}, {"time": 1.3333, "value": -14.8, "curve": [1.368, -13.84, 1.401, -13.07]}, {"time": 1.4333, "value": -13.07, "curve": [1.512, -13.07, 1.59, -16.28]}, {"time": 1.6667, "value": -20.5, "curve": [1.813, -28.22, 1.956, -39.21]}, {"time": 2.1, "value": -39.21, "curve": [2.29, -39.21, 2.479, -20.38]}, {"time": 2.6667, "value": -14.8}]}, "body9": {"rotate": [{"value": -20.79, "curve": [0.112, -22.13, 0.223, -25.3]}, {"time": 0.3333, "value": -27.72, "curve": [0.413, -29.38, 0.49, -30.72]}, {"time": 0.5667, "value": -30.72, "curve": [0.789, -30.72, 1.011, -20.14]}, {"time": 1.2333, "value": -20.14, "curve": [1.267, -20.14, 1.3, -20.39]}, {"time": 1.3333, "value": -20.79, "curve": [1.445, -22.13, 1.557, -25.3]}, {"time": 1.6667, "value": -27.72, "curve": [1.746, -29.38, 1.823, -30.72]}, {"time": 1.9, "value": -30.72, "curve": [2.122, -30.72, 2.344, -20.14]}, {"time": 2.5667, "value": -20.14, "curve": [2.6, -20.14, 2.633, -20.39]}, {"time": 2.6667, "value": -20.79}]}, "eye_L2": {"translate": [{"x": 0.16, "y": 3.81, "curve": "stepped"}, {"time": 0.3333, "x": 0.16, "y": 3.81, "curve": [0.356, 0.16, 0.378, 9.05, 0.356, 3.81, 0.378, 3.43]}, {"time": 0.4, "x": 9.05, "y": 3.43, "curve": "stepped"}, {"time": 0.6667, "x": 9.05, "y": 3.43, "curve": [0.689, 9.05, 0.711, 1.43, 0.689, 3.43, 0.711, 3.75]}, {"time": 0.7333, "x": 1.43, "y": 3.75, "curve": "stepped"}, {"time": 1.3333, "x": 1.43, "y": 3.75, "curve": [1.356, 1.43, 1.378, -8.09, 1.356, 3.75, 1.378, 4.17]}, {"time": 1.4, "x": -8.09, "y": 4.17, "curve": "stepped"}, {"time": 2, "x": -8.09, "y": 4.17}, {"time": 2.0667, "x": 0.16, "y": 3.81}]}, "eye_L3": {"translatex": [{"value": -2.6, "curve": "stepped"}, {"time": 0.2, "value": -2.6, "curve": [0.311, -2.6, 0.222, -0.23]}, {"time": 0.3333, "value": -0.23, "curve": "stepped"}, {"time": 0.3667, "value": -0.23}, {"time": 0.5, "value": -2.6, "curve": "stepped"}, {"time": 1.2333, "value": -2.6, "curve": [1.344, -2.6, 1.256, -0.23]}, {"time": 1.3667, "value": -0.23, "curve": "stepped"}, {"time": 1.4, "value": -0.23}, {"time": 1.5333, "value": -2.6}], "translatey": [{"value": 7.01, "curve": "stepped"}, {"time": 0.2, "value": 7.01, "curve": [0.256, 7.01, 0.278, 0.62]}, {"time": 0.3333, "value": 0.62, "curve": "stepped"}, {"time": 0.3667, "value": 0.62}, {"time": 0.5, "value": 7.01, "curve": "stepped"}, {"time": 1.2333, "value": 7.01, "curve": [1.29, 7.01, 1.311, 0.62]}, {"time": 1.3667, "value": 0.62, "curve": "stepped"}, {"time": 1.4, "value": 0.62}, {"time": 1.5333, "value": 7.01}], "scale": [{"x": 1.081}]}, "ik_rigth3": {"translatex": [{"value": -0.83, "curve": "stepped"}, {"time": 1.1667, "value": -0.83}], "translatey": [{"value": -5.29, "curve": "stepped"}, {"time": 0.2333, "value": -5.29, "curve": [0.289, -2.57, 0.311, 2.88]}, {"time": 0.3667, "value": 2.88, "curve": "stepped"}, {"time": 0.4, "value": 2.88, "curve": [0.444, 2.88, 0.489, -2.57]}, {"time": 0.5333, "value": -5.29, "curve": "stepped"}, {"time": 1.1667, "value": -5.29, "curve": [1.222, -2.57, 1.278, 2.88]}, {"time": 1.3333, "value": 2.88, "curve": "stepped"}, {"time": 1.3667, "value": 2.88, "curve": [1.411, 2.88, 1.456, -2.57]}, {"time": 1.5, "value": -5.29}], "scale": [{"x": 1.111}]}, "ik_rigth4": {"translate": [{"x": 0.18, "y": -3.81, "curve": "stepped"}, {"time": 0.3333, "x": 0.18, "y": -3.81, "curve": [0.356, 0.18, 0.378, -8.71, 0.356, -3.81, 0.378, -4.23]}, {"time": 0.4, "x": -8.71, "y": -4.23, "curve": "stepped"}, {"time": 0.6667, "x": -8.71, "y": -4.23, "curve": [0.689, -8.71, 0.711, -1.09, 0.689, -4.23, 0.711, -3.87]}, {"time": 0.7333, "x": -1.09, "y": -3.87, "curve": "stepped"}, {"time": 1.3333, "x": -1.09, "y": -3.87, "curve": [1.356, -1.09, 1.378, 8.43, 1.356, -3.87, 1.378, -3.42]}, {"time": 1.4, "x": 8.43, "y": -3.42, "curve": "stepped"}, {"time": 2, "x": 8.43, "y": -3.42}, {"time": 2.0667, "x": 0.18, "y": -3.81}]}, "eye_L4": {"translate": [{"x": 15.13, "y": -38.75, "curve": "stepped"}, {"time": 0.2, "x": 15.13, "y": -38.75}, {"time": 0.3333, "x": -0.96, "y": 2.45, "curve": "stepped"}, {"time": 0.3667, "x": -0.96, "y": 2.45, "curve": [0.411, -0.96, 0.456, 15.13, 0.411, 2.45, 0.456, -38.75]}, {"time": 0.5, "x": 15.13, "y": -38.75, "curve": "stepped"}, {"time": 1.2, "x": 15.13, "y": -38.75}, {"time": 1.3333, "x": -0.96, "y": 2.45, "curve": "stepped"}, {"time": 1.3667, "x": -0.96, "y": 2.45, "curve": [1.411, -0.96, 1.456, 15.13, 1.411, 2.45, 1.456, -38.75]}, {"time": 1.5, "x": 15.13, "y": -38.75}], "scale": [{"x": 1.02, "curve": "stepped"}, {"time": 0.2, "x": 1.02, "curve": [0.244, 1.02, 0.289, 1, 0.244, 1, 0.289, 1]}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667, "curve": [0.411, 1, 0.456, 1.02, 0.411, 1, 0.456, 1]}, {"time": 0.5, "x": 1.02, "curve": "stepped"}, {"time": 1.2, "x": 1.02, "curve": [1.244, 1.02, 1.289, 1, 1.244, 1, 1.289, 1]}, {"time": 1.3333, "curve": "stepped"}, {"time": 1.3667, "curve": [1.411, 1, 1.456, 1.02, 1.411, 1, 1.456, 1]}, {"time": 1.5, "x": 1.02}]}, "ik_rigth5": {"translate": [{"x": -0.37, "y": 28.68, "curve": "stepped"}, {"time": 0.2, "x": -0.37, "y": 28.68}, {"time": 0.3333, "x": 2.23, "y": 1.34, "curve": "stepped"}, {"time": 0.3667, "x": 2.23, "y": 1.34}, {"time": 0.5, "x": -0.37, "y": 28.68, "curve": "stepped"}, {"time": 1.2, "x": -0.37, "y": 28.68}, {"time": 1.3333, "x": 2.23, "y": 1.34, "curve": "stepped"}, {"time": 1.3667, "x": 2.23, "y": 1.34}, {"time": 1.5, "x": -0.37, "y": 28.68}], "scale": [{"x": 1.236, "curve": "stepped"}, {"time": 0.2, "x": 1.236, "curve": [0.244, 1.236, 0.289, 1.087, 0.244, 1, 0.289, 1]}, {"time": 0.3333, "x": 1.087, "curve": "stepped"}, {"time": 0.3667, "x": 1.087, "curve": [0.389, 1.087, 0.444, 1.158, 0.389, 1, 0.444, 1]}, {"time": 0.5, "x": 1.236, "curve": "stepped"}, {"time": 1.2, "x": 1.236, "curve": [1.244, 1.236, 1.289, 1.087, 1.244, 1, 1.289, 1]}, {"time": 1.3333, "x": 1.087, "curve": "stepped"}, {"time": 1.3667, "x": 1.087, "curve": [1.389, 1.087, 1.444, 1.158, 1.389, 1, 1.444, 1]}, {"time": 1.5, "x": 1.236}]}, "ik_left": {"translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": [0.222, 0, 0.278, 0, 0.222, 0, 0.278, -2.62]}, {"time": 0.3333, "y": -2.62, "curve": [0.389, 0, 0.444, 0, 0.389, -2.62, 0.444, 5.55]}, {"time": 0.5, "y": 6.24, "curve": [0.556, 0, 0.611, -0.69, 0.556, 6.94, 0.611, 6.94]}, {"time": 0.6667, "x": -0.69, "y": 6.94, "curve": [0.733, -0.69, 0.8, 2.73, 0.733, 6.94, 0.8, 1.8]}, {"time": 0.8667, "x": 3.47, "y": 0.69, "curve": [0.967, 4.58, 1.067, 4.86, 0.967, -0.97, 1.067, -0.68]}, {"time": 1.1667, "x": 4.86, "y": -1.39, "curve": [1.222, 4.86, 1.278, 0, 1.222, -1.78, 1.278, -2.62]}, {"time": 1.3333, "y": -2.62, "curve": [1.389, 0, 1.444, 0, 1.389, -2.62, 1.444, -0.87]}, {"time": 1.5}]}, "ik_rigth6": {"rotate": [{"value": 3.45}], "translate": [{"x": -11.38, "y": -25.71}]}, "eye_L": {"translate": [{"y": 1.8, "curve": "stepped"}, {"time": 0.3333, "y": 1.8, "curve": [0.378, 0, 0.422, 3.05, 0.378, 1.8, 0.422, 1.67]}, {"time": 0.4667, "x": 3.05, "y": 1.67, "curve": "stepped"}, {"time": 0.6667, "x": 3.05, "y": 1.67, "curve": [0.722, 3.05, 0.778, -2.28, 0.722, 1.67, 0.778, 1.9]}, {"time": 0.8333, "x": -2.28, "y": 1.9, "curve": "stepped"}, {"time": 1.1667, "x": -2.28, "y": 1.9, "curve": [1.233, -2.28, 1.3, -2.25, 1.233, 1.9, 1.3, 2.66]}, {"time": 1.3667, "x": -2.25, "y": 2.66, "curve": "stepped"}, {"time": 2, "x": -2.25, "y": 2.66, "curve": [2.078, -2.25, 2.156, -0.75, 2.078, 2.66, 2.156, 2.09]}, {"time": 2.2333, "y": 1.8}]}, "ik_rigth2": {"translate": [{"y": -1.76, "curve": "stepped"}, {"time": 0.5333, "y": -1.76, "curve": [0.578, 0, 0.622, -3.05, 0.578, -1.76, 0.622, -1.9]}, {"time": 0.6667, "x": -3.05, "y": -1.9, "curve": "stepped"}, {"time": 0.9, "x": -3.05, "y": -1.9, "curve": [0.956, -3.05, 1.011, 2.28, 0.956, -1.9, 1.011, -1.65]}, {"time": 1.0667, "x": 2.28, "y": -1.65, "curve": "stepped"}, {"time": 1.4, "x": 2.28, "y": -1.65, "curve": [1.467, 2.28, 1.533, 2.32, 1.467, -1.65, 1.533, -2.41]}, {"time": 1.6, "x": 2.32, "y": -2.41, "curve": "stepped"}, {"time": 2.0667, "x": 2.32, "y": -2.41, "curve": [2.144, 2.32, 2.222, 0.77, 2.144, -2.41, 2.222, -1.98]}, {"time": 2.3, "y": -1.76}]}, "eye_L5": {"rotate": [{"value": 73.52}], "translate": [{"x": 32.24, "y": 28.65}]}, "mauth3": {"rotate": [{"value": -15.59}], "translate": [{"y": 5.34}]}, "mauth2": {"translate": [{"y": -1.88}], "scale": [{"x": 1.135}]}, "body3": {"translate": [{"x": -7.46}]}, "body5": {"translate": [{"x": 8.21}]}, "body8": {"translate": [{"x": 6.71}]}, "body4": {"translate": [{"x": -7.46}]}, "mauth4": {"rotate": [{"value": 5.17}], "translate": [{"y": -4.53}]}, "ik_rigth": {"translate": [{"time": 0.1667, "curve": [0.222, 0, 0.278, 0, 0.222, -0.86, 0.278, -1.88]}, {"time": 0.3333, "y": -2.57, "curve": [0.389, 0, 0.444, 2.08, 0.389, -3.27, 0.444, -3.78]}, {"time": 0.5, "x": 2.08, "y": -4.16, "curve": [0.556, 2.08, 0.611, -0.17, 0.556, -4.54, 0.611, -4.86]}, {"time": 0.6667, "x": -0.69, "y": -4.86, "curve": [0.733, -1.32, 0.8, -1.39, 0.733, -4.86, 0.8, 6.71]}, {"time": 0.8667, "x": -1.39, "y": 7.63, "curve": [0.967, -1.39, 1.067, 0, 0.967, 9.02, 1.067, 9.02]}, {"time": 1.1667, "y": 9.02, "curve": [1.222, 0, 1.278, 0, 1.222, 9.02, 1.278, -2.57]}, {"time": 1.3333, "y": -2.57, "curve": [1.389, 0, 1.444, 0, 1.389, -2.57, 1.444, -0.86]}, {"time": 1.5}]}}}}}