// This file is auto-generated by @hey-api/openapi-ts

/**
 * Represents the current state and progress of an achievement for a player.
 */
export type Achievement = {
  /**
   * Current level the player has reached for this achievement.
   */
  currentLevel: number
  /**
   * Current reward multiplier for this achievement level.
   */
  currentMultiplier: number
  /**
   * Current progress value toward next level.
   */
  currentProgress: number
  /**
   * Progress requirement to reach next level.
   */
  currentRequirement: number
  /**
   * Unique identifier for this achievement type.
   */
  id: number
  /**
   * Whether this achievement has been fully claimed.
   */
  isClaimed: boolean
  /**
   * List of achievement levels ready to be claimed.
   */
  readyToClaim: Array<LevelReadyToClaim>
}

/**
 * Response containing a list of unattained achievements.
 */
export type AchievementsListResponse = {
  /**
   * The list of unattained achievements.
   */
  achievements: Array<UnattainedAchievement>
}

/**
 * Response containing a list of achievements and their current status for a player.
 */
export type AchievementsStateResponse = {
  /**
   * Vector of all achievements and their progress.
   */
  achievements: Array<Achievement>
}

export type BattleEventMeResponse = {
  leagueLevel: number
  rank?: number | null
  reward?: null | EventReward
  teamSkin?: number | null
  totalScore?: number | null
}

export type BattleEventReward = {
  coinsCollected?: number | null
  enemyScore: number
  enemySkin: number
  isWinner: boolean
  playerRank?: number | null
  reward?: null | EventReward
  teamScore: number
  teamSkin: number
}

export type BattleEventState = {
  endsAt: number
  hasLeaderboard: boolean
  hasMetRequirement: boolean
  leagueLock: boolean
  playerRank?: number | null
  startedAt: number
}

export type BattleLeadersResponse = {
  enemyTeamScore: number
  enemyTeamSkin?: number | null
  list: Array<EventLeader>
  teamScore: number
  teamSkin?: number | null
}

export type BoostersView = {
  stackableAimbot: number
  stackableJumper: number
  stackableMagneticField: number
  timeBoundAimbotsActiveTill?: number | null
  timeBoundJumpersActiveTill?: number | null
  timeBoundMagneticFieldActiveTill?: number | null
  timeBoundMagneticFieldRadius?: number | null
}

export type CancelWithdrawRequest = {
  withdrawId: string
}

export type CancelWithdrawResponse = {
  ok: boolean
}

export type CheckGlobalTaskResponse = {
  ok: boolean
}

export type CheckTonInvoiceRequest = {
  invoice: string
}

export type CheckTonInvoiceResponse = {
  isPaid: boolean
}

/**
 * Request to claim a completed achievement level.
 */
export type ClaimAchievementRequest = {
  /**
   * ID of the achievement to claim.
   */
  id: number
}

/**
 * Response after claiming an achievement level.
 */
export type ClaimAchievementResponse = {
  /**
   * The new total multiplier value after claiming
   */
  newMultiplier: number
}

export type ClaimAllSkinsCollectionsResponse = {
  ok: boolean
}

export type ClaimBattleEventBoosterRequest = {
  boosterId: number
}

export type ClaimBattleEventBoosterResponse = {
  ok: boolean
}

export type ClaimDailyRewardRequest = {
  [key: string]: unknown
}

export type ClaimDailyRewardResponse = {
  rewards: Array<RewardInfo>
}

export type ClaimDailyTaskRequest = {
  taskId: number
}

export type ClaimDailyTaskResponse = {
  reward: RewardInfo
}

export type ClaimFarmingRequest = {
  [key: string]: unknown
}

export type ClaimFarmingResponse = {
  farmedTickets: number
  leaguesToClaim: Array<number>
}

export type ClaimGlobalTaskRequest = {
  taskId: number
}

export type ClaimGlobalTaskResponse = {
  leaguesToClaim: Array<number>
  reward: RewardInfo
}

export type ClaimLeagueRewardResponse = {
  rewards: Array<RewardInfo>
}

export type ClaimReferralRewardRequest = {
  [key: string]: unknown
}

export type ClaimReferralRewardResponse = {
  leaguesToClaim: Array<number>
  ticketsClaimed: number
}

export type ClaimSkinRequest = {
  skinId: number
}

export type ClaimSkinResponse = {
  ok: boolean
}

export type ClaimSkinsCollectionResponse = {
  ok: boolean
}

export type ClaimWithdrawTaskRequest = {
  taskId: number
}

export type ClaimWithdrawTonResponse = {
  ok: boolean
}

export type ClanEventLeadersResponse = {
  list: Array<EventLeader>
}

export type ClanEventMeResponse = {
  leagueLevel: number
  rank?: number | null
  reward?: null | EventReward
  ticketsCollected?: number | null
}

export type ClanEventReward = {
  reward?: null | EventReward
  ticketsCollected: number
}

export type ClanEventState = {
  clanId: number
  endsAtEpochSec: number
  leagueLock: boolean
  playerRank?: number | null
}

export type ClanRatingItem = {
  id: number
  membersCount: number
  name: string
  rating: number
}

export type ClanRatingResponse = {
  clanId: number
  clanLink?: string | null
  clanName: string
  eventEndsAtEpochSec?: number | null
  isClanLeader: boolean
  isEventPossible?: boolean | null
  list: Array<UserClanRatingItem>
  membersCount: number
  rating: number
}

export type ClansRatingResponse = {
  list: Array<ClanRatingItem>
}

export type CompleteGlobalTaskRequest = {
  taskId: number
}

export type CompleteGlobalTaskResponse = {
  ok: boolean
}

export type ConnectWalletRequest = {
  address: string
}

export type ConnectWalletResponse = {
  ok: boolean
  wallet: string
}

export type ContestRequirement =
  | 'tickets'
  | 'friends'
  | 'multiplier'
  | 'skin'
  | 'task'
  | 'starsTotal'
  | 'starsDuringContest'

export type ContestRequirementStatus = {
  completed: boolean
  requirement: ContestRequirement
  value: ContestRequirementValue
}

export type ContestRequirementValue = string | number

export type ContestResponse = {
  contestId: number
  endsAt: number
  participantsAmount: number
  requirements: Array<ContestRequirementStatus>
  requirementsCompleted: boolean
  startsAt: number
}

export type ConvertCustomCoinsRequest = {
  [key: string]: unknown
}

export type ConvertCustomCoinsResponse = {
  toncoin: number
}

export type CreateInvoiceRequest = {
  currency: ExternalCurrencyType
  itemId: number
}

export type CreateInvoiceResponse = {
  invoice: string
}

export type CreateReferralLinkRequest = {
  [key: string]: unknown
}

export type CreateReferralLinkResponse = {
  reflink: string
}

export type CreateReviveInvoiceByStarsRequest = {
  sessionId: number
}

export type CreateReviveInvoiceByStarsResponse = {
  invoice: string
}

export type CreateSessionRequest = {
  session: string
}

export type CreateSessionResponse = {
  session: string
}

export type CreateWithdrawRequest = {
  amount: number
  currency: string
}

export type CreateWithdrawResponse = {
  withdraw?: null | Withdraw
}

export type Currency =
  | 'stars'
  | 'ton'
  | 'soft'
  | 'hard'
  | 'usd'
  | 'magicHorns'
  | 'dynamicCoins'
  | 'tickets'
  | 'puzzleCoins'

export type CustomCoinEventMeResponse = {
  coinsCollected?: number | null
  leagueLevel: number
  rank?: number | null
  reward?: null | EventReward
}

export type CustomCoinEventReward = {
  coinsCollected?: number | null
  playerRank?: number | null
  reward?: null | EventReward
}

export type CustomCoinEventState = {
  banner?: null | EventBannerType
  endsAt: number
  hasLeaderboard: boolean
  hasMetRequirement: boolean
  leagueLock: boolean
  playerRank?: number | null
  startedAt: number
}

export type CustomCoinLeadersResponse = {
  list: Array<EventLeader>
}

export type DailyRewardItem = {
  claimed: boolean
  day: number
  reward?: null | RewardInfo
  rewards?: Array<RewardInfo> | null
}

export type DailyRewards = {
  currentDay: number
  invitedRefs?: number | null
  milestones: Array<DailyRewardItem>
  rewards: Array<DailyRewardItem>
}

export type DailyTask = {
  claimed: boolean
  completed: boolean
  current: number
  name: string
  reward: RewardInfo
  target: number
  taskId: number
}

export type DailyTasksResponse = {
  beginningTasks: Array<DailyTask>
  lootboxAvailable: boolean
  lootboxProgress: number
  tasks: Array<DailyTask>
}

export type DailyWriteOffState = {
  heistEndResult?: boolean | null
  mobsToKill?: number | null
  timestampToFail?: number | null
  tonLockedBalance?: number | null
}

export type DisconnectWalletRequest = {
  [key: string]: unknown
}

export type DisconnectWalletResponse = {
  ok: boolean
}

export type EventBannerType = 'skins' | 'lootBox'

export type EventBoostersResponse = {
  paidBoosters: Array<PaidEventBooster>
  taskBoosters: Array<TaskEventBooster>
}

export type EventLeader = {
  id: number
  leagueLevel: number
  name: string
  reward?: null | EventReward
  value: number
}

/**
 * Represents a reward that can be earned from an event.
 * Contains the currency type and amount of the reward.
 */
export type EventReward = {
  /**
   * The amount of currency to be awarded. For unlimitedLives, it's the number of seconds
   */
  amount: number
  /**
   * The type of currency for this reward
   */
  currency: EventRewardCurrency
}

/**
 * Represents the different types of currencies that can be awarded as event rewards.
 * Used to specify the currency type for event reward responses.
 */
export type EventRewardCurrency =
  | 'soft'
  | 'hard'
  | 'tickets'
  | 'ton'
  | 'unlimitedLives'
  | 'magicHorns'
  | 'dynamicCoins'
  | 'wheelSpins'
  | 'puzzleCoins'

/**
 * Represents the different types of events that can be boosted.
 * Currently only supports Battle events.
 */
export type EventType = 'battle'

export type ExternalCurrencyType = 'stars' | 'ton'

/**
 * Response containing the configuration of the fortune wheel.
 * Includes information about each sector and its associated reward.
 */
export type FortuneWheelConfigResponse = {
  /**
   * List of sectors on the fortune wheel and their rewards
   */
  sectors: Array<FortuneWheelSector>
}

/**
 * Response returned when rolling the fortune wheel.
 * Contains the sector number that was landed on and the reward that was won.
 */
export type FortuneWheelRollResponse = {
  /**
   * The reward that was won.
   */
  reward: RewardInfo
  /**
   * The sector number (1-8) that the wheel landed on.
   */
  sector: number
}

/**
 * Represents a single sector on the fortune wheel.
 * Contains information about the sector number, reward type, and display text.
 */
export type FortuneWheelSector = {
  /**
   * Text to display for this sector's reward
   */
  displayText: string
  /**
   * The sector number (1-8) on the wheel
   */
  sector: number
  /**
   * The type of reward for this sector (Tickets or TON)
   */
  type: RewardType
}

/**
 * Represents requirements that must be met to participate in a game event.
 */
export type GameEventRequirement =
  | {
      referrals: number
    }
  | {
      clan: number
    }

export type HotRecordEventReward = {
  highestScore?: number | null
  reward?: null | EventReward
}

export type HotRecordEventState = {
  endsAt: number
  hasMetRequirement: boolean
  leagueLock: boolean
  playerRank?: number | null
  requirement?: null | GameEventRequirement
  rewardCurrency?: null | EventRewardCurrency
  rewardMaxAmount?: number | null
  startedAt: number
}

export type HotRecordLeadersResponse = {
  list: Array<EventLeader>
}

export type HotRecordMeResponse = {
  highestScore?: number | null
  leagueLevel: number
  rank?: number | null
  reward?: null | EventReward
}

export type InGameCurrencyType =
  | 'hard'
  | 'soft'
  | 'tickets'
  | 'magicHorns'
  | 'dynamicCoins'
  | 'puzzleCoins'

export type JackpotCouponTier = {
  coupons: number
  isAvailable: boolean
  isPurchased: boolean
  price: Price
  tier: number
}

export type JackpotEvent = {
  couponTiers: Array<JackpotCouponTier>
  currentCoupons: number
  endsAt: number
  maxCoupons: number
  purchaseEndsAt: number
  rewards: Array<JackpotReward>
  startedAt: number
}

export type JackpotLeaderboardResponse = {
  cursor?: string | null
  list: Array<EventLeader>
  me?: null | EventLeader
}

export type JackpotReward = {
  amount: number
  reward: EventReward
}

export type LeagueFeature =
  | 'farming'
  | 'dailyReward'
  | 'hotRecordEvent'
  | 'onePercentEvent'
  | 'tonMiningEvent'
  | 'offers'
  | 'customCoinEvent'
  | 'lives'
  | 'dynamicCoins'
  | 'puzzleCoins'
  | 'battleEvent'
  | 'clanEvent'

export type LeagueLeader = {
  id: number
  name: string
  value: number
}

export type LeagueLeadersResponse = {
  list: Array<LeagueLeader>
  rewards: Array<RewardInfo>
}

export type LeagueListItem = {
  leagueLevel: number
  name: string
  rewards: Array<RewardInfo>
  ticketsRange: Array<number>
  unlockFeatures: Array<LeagueFeature>
}

export type LeagueListResponse = {
  list: Array<LeagueListItem>
}

export type LeagueMeResponse = {
  leagueLevel: number
  leaguesToClaim: Array<number>
  name: string
  rank?: number | null
  rewards: Array<RewardInfo>
  tickets?: number | null
}

/**
 * Represents an achievement level that is ready to be claimed by the player.
 */
export type LevelReadyToClaim = {
  /**
   * The level number that can be claimed.
   */
  level: number
  /**
   * The reward multiplier for this level
   */
  multiplier: number
  /**
   * The progress requirement that was met
   */
  requirement: number
}

export type ListReferralsQuery = {
  cursor?: string | null
}

export type ListReferralsResponse = {
  cursor?: string | null
  list: Array<Referral>
}

export type ListShopResponse = {
  friends: Array<ShopItem>
  hard: Array<ShopItem>
  lootboxes: Array<ShopLootBoxItem>
  progressiveOffers: Array<ShopProgressiveOffer>
  puzzleOffers: Array<ShopPuzzleOffer>
  ransom?: null | ShopItem
  soft: Array<ShopItem>
  stackableBoosters: Array<ShopStackableBoosterItem>
  wheelSpins: Array<ShopItem>
}

export type ListSkinsCollectionResponse = {
  list: Array<Skin>
}

export type ListSkinsCollectionsResponse = {
  grantReward: RewardInfo
  isClaimed: boolean
  list: Array<SkinsCollection>
}

export type ListSkinsResponse = {
  list: Array<Skin>
}

export type LoginQuery = {
  initData: string
  reflink?: string | null
}

export type LoginResponse = {
  ok: boolean
  token?: string | null
}

export type LootBoxOffer = {
  id: number
  price: ShopItemPrice
  value: number
}

export type LootBoxType =
  | 'rainbowLootBox'
  | 'luckyLootBox'
  | 'fightLootBox'
  | 'duckyLootBox'
  | 'magicLootBox'

export type LootBoxesInfo = {
  availableLootboxes: {
    [key: string]: number
  }
  freeAvailableAt: number
}

export type OnePercentLeadersResponse = {
  list: Array<EventLeader>
}

export type OnePercentMeResponse = {
  leagueLevel: number
  rank?: number | null
  reward?: null | EventReward
  totalScore?: number | null
}

export type OnepercentEventReward = {
  reward?: null | EventReward
  totalScore?: number | null
}

export type OnepercentEventState = {
  endsAt: number
  hasMetRequirement: boolean
  leagueLock: boolean
  playerRank?: number | null
  requirement?: null | GameEventRequirement
  rewardCurrency?: null | EventRewardCurrency
  rewardMaxAmount?: number | null
  startedAt: number
  targetTotalScore: number
}

export type OpenLootBoxRequest = {
  lootboxType: LootBoxType
}

export type OpenLootBoxResponse = {
  lootboxType: LootBoxType
  rewards: Array<RewardInfo>
}

export type PaidEventBooster = {
  currentLevel: number
  currentMultiplier: number
  id: number
  maxLevel: number
  name: string
  nextLevel?: null | PaidEventBoosterLevel
  totalMultiplier: number
}

export type PaidEventBoosterLevel = {
  multiplier?: number | null
  price: Price
}

export type PlayerFlag =
  | 'onepercent_reward_received'
  | 'hotrecord_reward_received'
  | 'custom_coin_reward_received'
  | 'clan_event_reward_received'
  | 'battle_event_reward_received'
  | 'jackpot_reward_received'

export type PlayerProfileResponse = {
  created?: number | null
  gamesPlayed?: number | null
  highestScore?: number | null
  id: number
  leagueLevel?: number | null
  totalScore?: number | null
  transactionMade?: boolean | null
  wallet?: string | null
}

export type PlayerStateResponse = {
  battleCoin?: number | null
  battleEvent?: null | BattleEventState
  battleEventReward?: null | BattleEventReward
  boostersView?: null | BoostersView
  clanEventReward?: null | ClanEventReward
  clanEventState?: null | ClanEventState
  clanId?: number | null
  customCoin?: number | null
  customCoinConvertRate?: number | null
  customCoinEvent?: null | CustomCoinEventState
  customCoinEventReward?: null | CustomCoinEventReward
  dailyRewards?: null | DailyRewards
  dailyWriteOffState?: null | DailyWriteOffState
  dynamicCoins?: number | null
  farming?: null | TicketsFarming
  hard?: number | null
  hasUnclaimedAchievements?: boolean | null
  hasUnclaimedDailyTasks?: boolean | null
  hasUnclaimedTasks?: boolean | null
  hotrecordEvent?: null | HotRecordEventState
  hotrecordEventReward?: null | HotRecordEventReward
  isBeginnerTonExhausted?: boolean | null
  jackpotEvent?: null | JackpotEvent
  jackpotEventReward?: null | RewardInfo
  leagueLevel: number
  leaguesToClaim?: Array<number> | null
  liveReviveDuration: number
  lives?: number | null
  livesMax: number
  livesRevive?: number | null
  livesUnlimitedUntil?: number | null
  lootboxesInfo?: null | LootBoxesInfo
  magicHorns?: number | null
  maxTickets?: number | null
  multiplier?: number | null
  newTasks?: Array<string> | null
  onepercentEvent?: null | OnepercentEventState
  onepercentEventReward?: null | OnepercentEventReward
  progressiveOffers: Array<ProgressiveOfferInfo>
  puzzleCoins?: number | null
  puzzleOffers: Array<PuzzleOfferInfo>
  reflink?: string | null
  refs?: number | null
  refsFake?: number | null
  skin?: number | null
  soft?: number | null
  subscribedToChannel?: boolean | null
  tickets?: number | null
  ticketsUnclaimed?: number | null
  ton?: number | null
  tonOnPlatformEvent?: null | TonOnPlatformEventState
  tutorial?: boolean | null
  tutorialMobs?: boolean | null
  wheelSpins?: null | WheelSpinsInfo
}

export type PlayerTasksResponse = {
  tasks: Array<Task>
}

export type Price = {
  amount: number
  currency: Currency
}

export type ProgressiveOfferInfo = {
  endsAt: number
  id: number
  isCompleted: boolean
  startedAt: number
  usageDynamicCoins: boolean
}

export type ProgressiveOfferItem = {
  idx: number
  isAvailable: boolean
  isPurchased: boolean
  price: Price
  rewards: Array<RewardInfo>
}

export type PurchaseEventBoosterRequest = {
  boosterId: number
  event: EventType
}

export type PurchaseEventBoosterResponse = {
  invoiceLink?: string | null
  ok: boolean
}

export type PurchaseJackpotCouponsRequest = {
  tier: number
}

export type PurchaseJackpotCouponsResponse = {
  coupons: number
}

export type PurchaseProgressiveOfferRequest = {
  offerId: number
}

export type PurchaseProgressiveOfferResponse = {
  invoiceLink?: string | null
  ok: boolean
}

export type PurchasePuzzleOfferRequest = {
  itemIndex: number
  offerId: number
}

export type PurchasePuzzleOfferResponse = {
  invoiceLink?: string | null
  ok: boolean
}

export type PurchaseRequest = {
  currency: InGameCurrencyType
  itemId: number
}

export type PurchaseResponse = {
  ok: boolean
}

export type PurchaseReviveRequest = {
  currency: RevivalCurrencyType
  currentScore: number
  sessionId: number
}

export type PurchaseReviveResponse = {
  ok: boolean
}

export type PurchaseSkinRequest = {
  skinId: number
}

export type PurchaseSkinResponse = {
  ok: boolean
}

export type PuzzleOfferInfo = {
  endsAt: number
  id: number
  isCompleted: boolean
  startedAt: number
}

export type PuzzleOfferItem = {
  idx: number
  isPurchased: boolean
  price: Price
}

export type Referral = {
  id: number
  leagueLevel: number
  name: string
  premium: boolean
  ticketsClaimed?: number | null
  ticketsUnclaimed?: number | null
}

export type RemovePlayerFlagRequest = {
  flag: PlayerFlag
}

export type RemovePlayerFlagResponse = {
  [key: string]: unknown
}

/**
 * Specifies the type of currency that can be used to purchase a revive.
 */
export type RevivalCurrencyType = 'hard' | 'stars' | 'magicHorns'

/**
 * Describes the price and currency type required to purchase a revive.
 * To get more information about `price` calculation, see [`revival_price`] module.
 */
export type RevivalPrice = {
  /**
   * The amount of currency required, calculated based on the currency type and number of
   * revives used.
   */
  price: number
  /**
   * The type of currency that can be used.
   */
  type: RevivalCurrencyType
}

export type ReviveInfoRequest = {
  sessionId: number
}

/**
 * Response containing information about available revives and their prices.
 */
export type ReviveInfoResponse = {
  /**
   * Whether the free revive is available.
   */
  freeIsAvailable: boolean
  /**
   * If the user doesn't have enough hard currency, this field will contain
   * available in-game offers for purchasing hard currency.
   */
  hardOffers: Array<ShopItem>
  /**
   * If the user doesn't have enough magic horn, this field will contain
   * available in-game offers for purchasing magic horn.
   */
  magicHornOffers: Array<ShopItem>
  maxRevivalsAmount: number
  /**
   * List of prices for the next revive, which may use different currency types. Empty if no
   * revives are allowed
   */
  prices: Array<RevivalPrice>
  usedRevivalsAmount: number
}

export type RewardInfo = {
  /**
   * The multiplier of the reward
   */
  multiplier?: number | null
  /**
   * The type of reward (soft currency, hard currency, tickets, or unlimited lives)
   */
  type: RewardType
  /**
   * The value of the reward
   */
  value: number
}

/**
 * The different types of rewards that can be earned from tasks
 */
export type RewardType =
  | 'refsFake'
  | 'soft'
  | 'hard'
  | 'tickets'
  | 'unlimitedLives'
  | 'ton'
  | 'dynamicCoins'
  | 'puzzleCoins'
  | 'timeBoundMagneticField'
  | 'fullLives'
  | 'magicHorns'
  | 'rainbowLootBox'
  | 'luckyLootBox'
  | 'fightLootBox'
  | 'duckyLootBox'
  | 'magicLootBox'
  | 'skin'
  | 'stackableMagneticField'
  | 'stackableAimbot'
  | 'stackableJumper'
  | 'timeBoundAimbot'
  | 'timeBoundJumper'
  | 'lives'
  | 'customCoin'
  | 'battleCoin'
  | 'wheelSpins'

export type SelectSkinRequest = {
  skinId: number
}

export type SelectSkinResponse = {
  ok: boolean
}

export type ShopItem = {
  id: number
  price: ShopItemPrice
  value: number
}

export type ShopItemPrice = {
  displayPrice: Price
  prices: Array<Price>
}

export type ShopLootBoxItem = {
  availableAt?: number | null
  commonSkins: number
  epicSkins: number
  lootboxType: LootBoxType
  offers: Array<LootBoxOffer>
  /**
   * tickets, wheelSpins, magicHorns, stackableMagneticField, stackableAimbot, stackableJumper
   */
  otherRewards: Array<RewardType>
  rareSkins: number
}

export type ShopMagneticFieldItem = {
  durationSecs: number
  id: string
  price: Price
  radius: number
}

export type ShopProgressiveOffer = {
  currentStage: number
  dynamicCoins?: number | null
  endsAt: number
  id: number
  isCompleted: boolean
  items: Array<ProgressiveOfferItem>
  startedAt: number
  usageDynamicCoins: boolean
}

export type ShopPuzzleOffer = {
  currentStage: number
  endsAt: number
  id: number
  isCompleted: boolean
  items: Array<PuzzleOfferItem>
  puzzleCoins: number
  stageGrantPrize: Array<RewardInfo>
  startedAt: number
}

export type ShopStackableBoosterItem = {
  id: number
  price: ShopItemPrice
  type: StackableBoosterType
  value: number
}

export type Skin = {
  id: number
  locked: boolean
  multiplier: number
  price?: null | Price
  purchased: boolean
  rarity: SkinRarity
  readyToClaim?: boolean | null
  requiresBox?: null | LootBoxType
  requiresDailyReward?: boolean | null
  requiresRefs?: number | null
  requiresTransaction?: boolean | null
  requiresWallet?: boolean | null
  shopItem?: string | null
}

export type SkinRarity = 'common' | 'rare' | 'epic'

export type SkinsCollection = {
  id: number
  isClaimed: boolean
  ownedSkinsCount: number
  reward: RewardInfo
  totalSkinsCount: number
}

/**
 * Represents the type of stackable booster available on the client.
 */
export type StackableBoosterType = 'stackableMagneticField' | 'stackableJumper' | 'stackableAimbot'

export type StartClanEventResponse = {
  endsAtEpochSec?: number | null
  ok: boolean
}

export type StartFarmingRequest = {
  [key: string]: unknown
}

export type StartFarmingResponse = {
  changeRate: number
  endsAt: number
  startedAt: number
}

export type Task = {
  claimed: boolean
  completed: boolean
  name: string
  reward: RewardInfo
  taskId: number
}

export type TaskEventBooster = {
  currentLevel: number
  id: number
  isClaimed: boolean
  maxLevel: number
  multiplier: number
  name: string
}

export type TicketsFarming = {
  changeRate: number
  endsAt: number
  farmedTickets?: number | null
  farmedTicketsLastUpdate?: number | null
  startedAt: number
}

export type TonOnPlatformEventState = {
  endsAt: number
  hasEventSkin: boolean
  isTonAvailableForUser: boolean
  isTonSpawnEnabled: boolean
  leagueLock: boolean
  startedAt: number
}

export type TransactionInfo = {
  amount: number
  createdAt: string
  currency: ExternalCurrencyType
  id: string
  rewards: Array<RewardInfo>
  status: TransactionStatus
}

export type TransactionListResponse = {
  list: Array<TransactionInfo>
}

export type TransactionStatus = 'success' | 'failed'

export type UnattainedAchievement = {
  /**
   * The current progress of the achievement.
   */
  currentProgress: number
  /**
   * The ID of the achievement.
   */
  id: number
  /**
   * The levels that have not yet been completed.
   */
  levels: Array<UnattainedLevel>
}

export type UnattainedLevel = {
  /**
   * The level number of this achievement tier.
   */
  level: number
  /**
   * The reward multiplier for this level.
   */
  multiplier: number
  /**
   * The amount of progress required to complete this level.
   */
  requirement: number
}

export type UpdateSessionRequest = {
  update: string
}

export type UpdateSessionResponse = {
  ok: boolean
}

export type UserClanRatingItem = {
  firstName?: string | null
  id: number
  isClanLeader?: boolean | null
  isOnline: boolean
  lastName?: string | null
  lastTimeSeen?: number | null
  leagueLevel: number
  rating: number
}

export type UserProfileQuery = {
  id: number
}

export type UserProfileResponse = {
  clanId?: number | null
  clanName?: string | null
  firstName?: string | null
  gamesPlayed?: number | null
  highestScore?: number | null
  id: number
  lastName?: string | null
  leagueLevel?: number | null
  multiplier?: number | null
  skin?: number | null
  tickets?: number | null
  totalScore?: number | null
}

export type UtcResponse = {
  utc: number
}

export type WalletAsset = {
  amount: number
  currency: string
  usd: number
  withdrawMin: number
}

export type WalletAssetsResponse = {
  assets: Array<WalletAsset>
  tasks: Array<WithdrawTask>
  usdTotal: number
}

export type WheelSpinsInfo = {
  amount: number
  freeAvailableAt: number
}

export type Withdraw = {
  amount: number
  createdAt: string
  currency: string
  fee: number
  id: string
  status: string
  updatedAt: string
  wallet: string
}

export type WithdrawListResponse = {
  list: Array<Withdraw>
}

export type WithdrawTask = {
  currentValue: number
  id: number
  isClaimed: boolean
  name: string
  requiredValue: number
  reward: RewardInfo
}

export type WriteOffClaimResponse = {
  ok: boolean
}

export type WriteOffRansomDeniedResponse = {
  ok: boolean
  reason?: string | null
}

export type ClaimAchievementData = {
  body: ClaimAchievementRequest
}

export type ClaimAchievementResponse2 = ClaimAchievementResponse

export type ClaimAchievementError = unknown

export type GetAchievementsListResponse = AchievementsListResponse

export type GetAchievementsListError = unknown

export type GetAchievementsStateResponse = AchievementsStateResponse

export type GetAchievementsStateError = unknown

export type LoginData = {
  query?: {
    /**
     * Init data from Telegram
     */
    initData?: string
    /**
     * Referrer user id
     */
    referrer?: string
  }
}

export type LoginResponse2 = LoginResponse

export type LoginError = unknown

export type ClansRatingResponse2 = ClansRatingResponse

export type ClansRatingError = unknown

export type UsersInClanRatingData = {
  path: {
    /**
     * Clan id
     */
    clan_id: number
  }
}

export type UsersInClanRatingResponse = ClanRatingResponse

export type UsersInClanRatingError = ClanRatingResponse

export type GetContestInfoData = {
  path: {
    /**
     * Contest id
     */
    contest_id: number
  }
}

export type GetContestInfoResponse = ContestResponse

export type GetContestInfoError = unknown

export type GetBattleEventBoostersResponse = EventBoostersResponse

export type GetBattleEventBoostersError = unknown

export type ClaimBattleEventBoosterData = {
  body: ClaimBattleEventBoosterRequest
}

export type ClaimBattleEventBoosterResponse2 = ClaimBattleEventBoosterResponse

export type ClaimBattleEventBoosterError = unknown

export type GetBattleEventLeadersResponse = BattleLeadersResponse

export type GetBattleEventLeadersError = BattleLeadersResponse

export type GetBattleEventUserInfoResponse = BattleEventMeResponse

export type GetBattleEventUserInfoError = BattleEventMeResponse

export type GetClanEventLeadersResponse = ClanEventLeadersResponse

export type GetClanEventLeadersError = ClanEventLeadersResponse

export type GetClanEventUserInfoResponse = ClanEventMeResponse

export type GetClanEventUserInfoError = ClanEventMeResponse

export type StartClanEventResponse2 = StartClanEventResponse

export type StartClanEventError = StartClanEventResponse

export type ConvertCustomCoinsData = {
  body: ConvertCustomCoinsRequest
}

export type ConvertCustomCoinsResponse2 = ConvertCustomCoinsResponse

export type ConvertCustomCoinsError = unknown

export type GetCustomCoinsLeadersResponse = CustomCoinLeadersResponse

export type GetCustomCoinsLeadersError = CustomCoinLeadersResponse

export type GetCustomCoinEventUserInfoResponse = CustomCoinEventMeResponse

export type GetCustomCoinEventUserInfoError = CustomCoinEventMeResponse

export type GetHotrecordLeadersResponse = HotRecordLeadersResponse

export type GetHotrecordLeadersError = HotRecordLeadersResponse

export type GetHotrecordUserInfoResponse = HotRecordMeResponse

export type GetHotrecordUserInfoError = HotRecordMeResponse

export type GetJackpotLeaderboardData = {
  query?: {
    /**
     * Cursor for pagination
     */
    cursor?: string
  }
}

export type GetJackpotLeaderboardResponse = JackpotLeaderboardResponse

export type GetJackpotLeaderboardError = JackpotLeaderboardResponse

export type GetOnepercentLeadersResponse = OnePercentLeadersResponse

export type GetOnepercentLeadersError = OnePercentLeadersResponse

export type GetOnepercentUserInfoResponse = OnePercentMeResponse

export type GetOnepercentUserInfoError = OnePercentMeResponse

export type ClaimFarmingData = {
  body: ClaimFarmingRequest
}

export type ClaimFarmingResponse2 = ClaimFarmingResponse

export type ClaimFarmingError = ClaimFarmingResponse

export type StartFarmingData = {
  body: StartFarmingRequest
}

export type StartFarmingResponse2 = StartFarmingResponse

export type StartFarmingError = StartFarmingResponse

export type GetFortuneWheelConfigResponse = FortuneWheelConfigResponse

export type GetFortuneWheelConfigError = unknown

export type SpinWheelResponse = FortuneWheelRollResponse

export type SpinWheelError = unknown

export type ClaimLockedBalanceResponse = WriteOffClaimResponse

export type ClaimLockedBalanceError = WriteOffClaimResponse

export type DenyWriteOffRansomResponse = WriteOffRansomDeniedResponse

export type DenyWriteOffRansomError = WriteOffRansomDeniedResponse

export type CreateReviveInvoiceByStarsData = {
  body: CreateReviveInvoiceByStarsRequest
}

export type CreateReviveInvoiceByStarsResponse2 = CreateReviveInvoiceByStarsResponse

export type CreateReviveInvoiceByStarsError = unknown

export type GetReviveInfoData = {
  body: ReviveInfoRequest
}

export type GetReviveInfoResponse = ReviveInfoResponse

export type GetReviveInfoError = unknown

export type PurchaseReviveData = {
  body: PurchaseReviveRequest
}

export type PurchaseReviveResponse2 = PurchaseReviveResponse

export type PurchaseReviveError = unknown

export type CreateGameplaySessionOctetData = {
  /**
   * Raw binary payload
   */
  body: Array<number>
}

export type CreateGameplaySessionOctetResponse = unknown

export type CreateGameplaySessionOctetError = unknown

export type UpdateGameplaySessionOctetData = {
  /**
   * Raw binary payload
   */
  body: Array<number>
}

export type UpdateGameplaySessionOctetResponse = UpdateSessionResponse

export type UpdateGameplaySessionOctetError = unknown

export type GetLeagueListResponse = LeagueListResponse

export type GetLeagueListError = unknown

export type GetLeaguesUserInfoResponse = LeagueMeResponse

export type GetLeaguesUserInfoError = unknown

export type ClaimLeagueRewardData = {
  path: {
    /**
     * League id
     */
    league_id: number
  }
}

export type ClaimLeagueRewardResponse2 = ClaimLeagueRewardResponse

export type ClaimLeagueRewardError = unknown

export type GetLeaguesLeadersData = {
  path: {
    /**
     * League id
     */
    league_id: number
  }
}

export type GetLeaguesLeadersResponse = LeagueLeadersResponse

export type GetLeaguesLeadersError = unknown

export type GetFreeLootboxResponse = unknown

export type GetFreeLootboxError = unknown

export type OpenLootboxData = {
  body: OpenLootBoxRequest
}

export type OpenLootboxResponse = OpenLootBoxResponse

export type OpenLootboxError = unknown

export type ClaimDailyRewardData = {
  body: ClaimDailyRewardRequest
}

export type ClaimDailyRewardResponse2 = ClaimDailyRewardResponse

export type ClaimDailyRewardError = unknown

export type ClaimDailyTasksData = {
  body: ClaimDailyTaskRequest
}

export type ClaimDailyTasksResponse = ClaimDailyTaskResponse

export type ClaimDailyTasksError = unknown

export type ClaimDailyTasksLootboxResponse = unknown

export type ClaimDailyTasksLootboxError = unknown

export type GetDailyTasksResponse = DailyTasksResponse

export type GetDailyTasksError = unknown

export type RemovePlayerFlagData = {
  body: RemovePlayerFlagRequest
}

export type RemovePlayerFlagResponse2 = RemovePlayerFlagResponse

export type RemovePlayerFlagError = unknown

export type GetPlayerProfileResponse = PlayerProfileResponse

export type GetPlayerProfileError = unknown

export type GetPlayerStateResponse = PlayerStateResponse

export type GetPlayerStateError = unknown

export type CheckGlobalTaskData = {
  path: {
    /**
     * Task ID
     */
    taskId: number
  }
}

export type CheckGlobalTaskResponse2 = CheckGlobalTaskResponse

export type CheckGlobalTaskError = unknown

export type ClaimGlobalTaskData = {
  body: ClaimGlobalTaskRequest
}

export type ClaimGlobalTaskResponse2 = ClaimGlobalTaskResponse

export type ClaimGlobalTaskError = unknown

export type CompleteGlobalTaskData = {
  body: CompleteGlobalTaskRequest
}

export type CompleteGlobalTaskResponse2 = CompleteGlobalTaskResponse

export type CompleteGlobalTaskError = unknown

export type GetGlobalTasksResponse = PlayerTasksResponse

export type GetGlobalTasksError = unknown

export type GetUtcResponse = UtcResponse

export type GetUtcError = unknown

export type CreateReferralLinkData = {
  body: CreateReferralLinkRequest
}

export type CreateReferralLinkResponse2 = CreateReferralLinkResponse

export type CreateReferralLinkError = unknown

export type GetReferralsListData = {
  query?: {
    /**
     * Cursor
     */
    cursor?: string
  }
}

export type GetReferralsListResponse = ListReferralsResponse

export type GetReferralsListError = unknown

export type ClaimReferralRewardData = {
  body: ClaimReferralRewardRequest
}

export type ClaimReferralRewardResponse2 = ClaimReferralRewardResponse

export type ClaimReferralRewardError = unknown

export type CreateInvoiceData = {
  body: CreateInvoiceRequest
}

export type CreateInvoiceResponse2 = CreateInvoiceResponse

export type CreateInvoiceError = unknown

export type GetShopItemsResponse = ListShopResponse

export type GetShopItemsError = unknown

export type PurchaseData = {
  body: PurchaseRequest
}

export type PurchaseResponse2 = PurchaseResponse

export type PurchaseError = unknown

export type PurchaseEventBoosterData = {
  body: PurchaseEventBoosterRequest
}

export type PurchaseEventBoosterResponse2 = PurchaseEventBoosterResponse

export type PurchaseEventBoosterError = unknown

export type PurchaseJackpotCouponsData = {
  body: PurchaseJackpotCouponsRequest
}

export type PurchaseJackpotCouponsResponse2 = PurchaseJackpotCouponsResponse

export type PurchaseJackpotCouponsError = unknown

export type PurchaseProgressiveData = {
  body: PurchaseProgressiveOfferRequest
}

export type PurchaseProgressiveResponse = PurchaseProgressiveOfferResponse

export type PurchaseProgressiveError = unknown

export type PurchasePuzzleData = {
  body: PurchasePuzzleOfferRequest
}

export type PurchasePuzzleResponse = PurchasePuzzleOfferResponse

export type PurchasePuzzleError = unknown

export type PurchaseRansomResponse = PurchaseResponse

export type PurchaseRansomError = unknown

export type CheckTonInvoiceData = {
  body: CheckTonInvoiceRequest
}

export type CheckTonInvoiceResponse2 = CheckTonInvoiceResponse

export type CheckTonInvoiceError = unknown

export type ClaimSkinData = {
  body: ClaimSkinRequest
}

export type ClaimSkinResponse2 = ClaimSkinResponse

export type ClaimSkinError = unknown

export type ClaimAllCollectionsResponse = ClaimAllSkinsCollectionsResponse

export type ClaimAllCollectionsError = unknown

export type ClaimCollectionData = {
  path: {
    /**
     * Collection ID
     */
    collectionId: number
  }
}

export type ClaimCollectionResponse = ClaimSkinsCollectionResponse

export type ClaimCollectionError = unknown

export type GetCollectionData = {
  path: {
    /**
     * Collection ID
     */
    collectionId: number
  }
}

export type GetCollectionResponse = ListSkinsCollectionResponse

export type GetCollectionError = unknown

export type GetCollectionsResponse = ListSkinsCollectionsResponse

export type GetCollectionsError = unknown

export type GetSkinsResponse = ListSkinsResponse

export type GetSkinsError = unknown

export type PurchaseSkinData = {
  body: SelectSkinRequest
}

export type PurchaseSkinResponse2 = SelectSkinResponse

export type PurchaseSkinError = unknown

export type SelectSkinData = {
  body: SelectSkinRequest
}

export type SelectSkinResponse2 = SelectSkinResponse

export type SelectSkinError = unknown

export type GetUserProfileData = {
  query: {
    /**
     * User id
     */
    id: number
  }
}

export type GetUserProfileResponse = UserProfileResponse

export type GetUserProfileError = unknown

export type GetAssetsResponse = WalletAssetsResponse

export type GetAssetsError = unknown

export type ConnectWalletData = {
  body: ConnectWalletRequest
}

export type ConnectWalletResponse2 = ConnectWalletResponse

export type ConnectWalletError = unknown

export type DisconnectWalletData = {
  body: DisconnectWalletRequest
}

export type DisconnectWalletResponse2 = DisconnectWalletResponse

export type DisconnectWalletError = unknown

export type GetTransactionsHistoryResponse = TransactionListResponse

export type GetTransactionsHistoryError = unknown

export type CancelWithdrawData = {
  body: CancelWithdrawRequest
}

export type CancelWithdrawResponse2 = CancelWithdrawResponse

export type CancelWithdrawError = unknown

export type ClaimWithdrawTaskData = {
  body: ClaimWithdrawTaskRequest
}

export type ClaimWithdrawTaskResponse = ClaimWithdrawTonResponse

export type ClaimWithdrawTaskError = ClaimWithdrawTonResponse

export type CreateWithdrawData = {
  body: CreateWithdrawRequest
}

export type CreateWithdrawResponse2 = CreateWithdrawResponse

export type CreateWithdrawError = CreateWithdrawResponse

export type GetWithdrawHistoryResponse = WithdrawListResponse

export type GetWithdrawHistoryError = unknown
