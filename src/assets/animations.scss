@keyframes shine-pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

@keyframes shine-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.shine-pulse-animation {
  position: absolute;
  animation: shine-pulse 3s linear infinite;
}

.shine-rotate-animation {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: shine-rotate 30s linear infinite;
}

.shine-pulse-radial-animation {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(yellow 0%, transparent 60%);
  animation: shine-pulse 3s linear infinite;
}

@keyframes coin-move {
  30%, 50% {
    transform: translate(var(--first-move-to-x), var(--first-move-to-y));
  }
  100% {
    transform: translate(var(--second-move-to-x), var(--second-move-to-y));
  }
}
@keyframes coin-opacity {
  0% {
    opacity: 0;
  }
  25%, 70% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.animated-coin {
  --first-move-to-x: 0px;
  --first-move-to-y: 0px;
  --second-move-to-x: 0px;
  --second-move-to-y: 0px;
  --move-delay: 0s;

  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 1000;
  width: 32px;
  height: 32px;
  opacity: 0;
  animation: coin-move 1s ease forwards, coin-opacity 1s ease forwards;
  animation-delay: var(--move-delay);
}

.route-animation-enter-active,
.route-animation-leave-active {
  transition: opacity 0.2s ease;
}

.route-animation-enter-from,
.route-animation-leave-to {
  opacity: 0;
}
