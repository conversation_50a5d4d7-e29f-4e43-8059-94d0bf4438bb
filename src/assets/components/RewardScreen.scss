.reward-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: max(var(--inset-top), 48px) 0 10% 0 !important;

  &__timer {
    --reward-timer-duration: 3s;
    width: 156px;
    height: 5px;
    background-color: #D9D9D966;
    border-radius: 6px;

    &-bar {
      height: 100%;
      width: 100%;
      background-color: #D9D9D9;
      border-radius: 6px;

      animation: reward-timer var(--reward-timer-duration) linear forwards;
    }
  }

  &__bounce {
    &_active {
      animation: reward-bounce 0.5s ease-in-out;
      animation-delay: 0.1s;
    }
  }
}

@keyframes reward-timer {
  0% {
    width: 100%;
  }
  100% {
    width: 0%;
  }
}

@keyframes reward-bounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.advanced-reward {
  @keyframes reward-appear {
    0% {
      transform: translateY(500px)
    }

    60% {
      transform: translateY(-50px)
    }

    100% {
      transform: translateY(0)
    }
  }

  @keyframes reward-shadow-appear {
    0% {
      opacity: 0;
    }

    50% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  @keyframes reward-shine-animation {
    0%, 100% {
      opacity: 0.7;
      transform: scaleY(1);
    }

    50% {
      opacity: 1;
      transform: scaleY(1.05);
    }
  }

  @keyframes reward-trace-animation {
    0%, 100% {
      transform: scaleY(1);
    }

    50% {
      transform: scaleY(1.3);
    }
  }

  &__item_active {
    &::after {
      animation: reward-shadow-appear 0.9s alternate;
    }
  }

  &__shine {
    animation: reward-shine-animation 2s ease-in-out infinite;
  }

  &__pedestal {
    position: absolute;
    width: 110%;
    left: 50%;
    bottom: 4px;
    transform: translate(-50%, 50%);
    transform-origin: bottom right;

    &-circle {
      width: 100%;
      height: 51px;
      border-radius: 50%;
      background-color: white;
    }

    &-trace-wrapper {
      position: absolute;
      bottom: 10px;
      width: 15px;

      &:nth-child(1) {
        height: 150px;
        left: 15%;
        transform: rotate(-17deg);
        .skin-reward__pedestal-trace {
          animation-delay: 0.5s;
        }
      }

      &:nth-child(2) {
        height: 100px;
        left: 32%;
        transform: rotate(-13deg);
        .skin-reward__pedestal-trace {
          animation-delay: 0.2s;
        }
      }

      &:nth-child(3) {
        height: 240px;
        left: 47%;
        transform: rotate(-10deg);
        .skin-reward__pedestal-trace {
          animation-delay: 0.7s;
        }
      }

      &:nth-child(4) {
        height: 130px;
        left: 70%;
        transform: rotate(10deg);
        .skin-reward__pedestal-trace {
          animation-delay: 0s;
        }
      }

      &:nth-child(5) {
        height: 80px;
        left: 85%;
        transform: rotate(15deg);
        .skin-reward__pedestal-trace {
          animation-delay: 0.4s;
        }
      }
    }

    &-trace {
      width: 100%;
      height: 100%;
      border-radius: 50% 50% 50% 50% / 88% 88% 12% 12%;
      background-color: white;
      transform-origin: bottom right;
      animation: reward-trace-animation 0.3s ease-in infinite;
    }
  }
}
