.icon-bg {
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}

.soft-coin-bg {
  background-image: url('@/assets/images/temp/currency/soft-coin.png');
}
.hard-coin-bg {
  background-image: url('@/assets/images/temp/currency/hard-coin.png');
}
.tg-star-bg {
  background-image: url('@/assets/images/temp/currency/tg-star.png');
}
.horn-bg {
  position: relative;
  overflow: visible;

  &::before {
    content: '';
    position: absolute;
    width: 120%;
    height: 120%;
    transform: translateY(-10%);
    background-repeat: no-repeat;
    background-position: center;
    background-image: url('@/assets/images/temp/currency/horn.png');
    background-size: contain;
  }
}
.ticket-bg {
  background-image: url('@/assets/images/temp/currency/ticket.png');
  transform: rotate(-25deg);
}
.coupon-bg {
  background-image: url('@/assets/images/temp/jackpot/coupons.png');
}
.ref-bg {
  background-image: url('@/assets/images/temp/uni-ref.png');
}
.ton-bg {
  background-image: url('@/assets/images/temp/currency/ton.png');
}
.dollar-bg {
  margin: 0 -6px;
  &::before {
    content: '$ ';
  }
}
.multiplier-bg {
  background-image: url('@/assets/images/temp/multiplier-icon.png');
}
.multiplier-tickets-bg {
  background-image: url('@/assets/images/temp/x_tickets.png');
}
.heart-bg {
  background-image: url('@/assets/images/temp/heart.png');
}
.wheel-bg {
  background-image: url('@/assets/images/temp/currency/wheel-spin.png');

  &_simple {
    background-image: url('@/assets/images/temp/wheel-spin/spin.png');
  }
}
.magnet-bg {
  position: relative;
  overflow: visible;

  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: center;
    background-image: url('@/assets/images/temp/boosters/magnet.png');
    background-size: contain;
  }

  &_simple {
    background-image: url('@/assets/images/temp/boosters/magnet.png');
  }
}
.aim-bg {
  background-image: url('@/assets/images/temp/boosters/aim.png');
}
.jumper-bg {
  background-image: url('@/assets/images/temp/boosters/jumper.png');
}
.unlimited-heart-bg {
  background-image: url('@/assets/images/temp/unlimited-heart.png');
}
.store-bg {
  background-image: url('@/assets/images/temp/store.png');
}
.custom-coin-bg {
  background-image: url('@/assets/images/temp/custom-coin-event/coin.png');
}
.battle-coin-bg {
  background-image: url('@/assets/images/temp/battle-event/coin.png');
}
.dynamic-coin-bg {
  &_moon {
    background-image: url('@/assets/images/temp/deep-dive/dynamic-coin-moon.png');
  }
  &_ocean {
    background-image: url('@/assets/images/temp/deep-dive/dynamic-coin-ocean.png');
  }
}
.puzzle-coin-bg {
  background-image: url('@/assets/images/temp/fragment/coin.png');
}
.rainbow-box-bg {
  background-image: url('@/assets/images/temp/big-icons/rainbowBox.png');
  transform: translateY(-5%);
}
.lucky-box-bg {
  background-image: url('@/assets/images/temp/big-icons/luckyBox.png');
  transform: translateY(-5%);
}

.close-button {
  position: absolute;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  --close-btn-background-color: #ffffff;

  &::before {
    content: '';
    position: absolute;
    transform: rotate(45deg);
    width: 100%;
    height: 3px;
    background: var(--close-btn-background-color);
    border-radius: 9999px;
  }

  &::after {
    content: '';
    position: absolute;
    transform: rotate(-45deg);
    width: 100%;
    height: 3px;
    background: var(--close-btn-background-color);
    border-radius: 9999px;
  }
}
