import { useSkinsAchievement } from '@/composables/useAchievementsCheck'
import { getCurrencyRealAmount } from '@/constants/currency'
import { useOpenLootbox } from '@/services/client/useLootboxes'
import { addToPlayerState, usePlayerState } from '@/services/client/usePlayerState'
import type { RewardInfo, RewardType } from '@/services/openapi'
import { useLootboxReward, useRewardStore, useSkinReward } from '@/stores/rewardStore'
import type {
  LootboxRewardInfo,
  NumeralRewardInfo,
  Reward,
  SimpleRewardInfo,
  SkinRewardInfo,
  TimeRewardInfo
} from '@/types'
import {
  isLootboxRewardType,
  isNumeralRewardType,
  isObjectRewardType,
  isSimpleRewardType,
  isSkinRewardType,
  isStackableRewardType,
  isTimeRewardType
} from '@/types'

export const useReward = () => {
  const { playerState, refetchPlayerState } = usePlayerState()
  const { updatePlayerState } = addToPlayerState()
  const { checkSkinsAchievement } = useSkinsAchievement()
  const { openLootbox } = useOpenLootbox()
  const rewardStore = useRewardStore()
  const lootBoxStore = useLootboxReward()
  const skinStore = useSkinReward()

  const getCurrentValue = (rewardType: RewardType, playerState: any) => {
    if (isStackableRewardType(rewardType)) {
      return playerState.boostersView?.[rewardType] ?? 0
    } else if (isObjectRewardType(rewardType)) {
      return playerState[rewardType]?.amount ?? 0
    } else {
      return getCurrencyRealAmount(playerState[rewardType] ?? 0, rewardType)
    }
  }

  const showRewards = (
    reward: RewardInfo[],
    options: { shouldUpdatePlayerState?: boolean } = { shouldUpdatePlayerState: true }
  ) => {
    return new Promise<void>(resolve => {
      const numeralRewards = reward.filter((r): r is NumeralRewardInfo =>
        isNumeralRewardType(r.type)
      )
      const lootboxRewards = reward.filter((r): r is LootboxRewardInfo =>
        isLootboxRewardType(r.type)
      )
      const timeRewards = reward.filter((r): r is TimeRewardInfo => isTimeRewardType(r.type))
      const simpleRewards = reward.filter((r): r is SimpleRewardInfo => isSimpleRewardType(r.type))
      const skinRewards = reward.filter((r): r is SkinRewardInfo => isSkinRewardType(r.type))

      const rewards: Reward[] = [
        ...numeralRewards.map(r => {
          const value = getCurrencyRealAmount(r.value, r.type)
          const prevValue = getCurrentValue(r.type, playerState.value)
          return {
            type: r.type,
            value: value,
            prevValue: prevValue
          }
        }),
        ...timeRewards.map(r => ({
          type: r.type,
          duration: r.value
        })),
        ...simpleRewards.map(r => ({
          type: r.type
        })),
        ...lootboxRewards.map(r => ({
          type: r.type,
          value: r.value
        }))
      ]

      const numericPromise = () =>
        new Promise<void>(resolve => {
          if (rewards.length === 1 && isLootboxRewardType(rewards[0].type)) return
          rewardStore.showReward(rewards, () => {
            if (options.shouldUpdatePlayerState) {
              if (timeRewards.length || rewards.find(r => r.type === 'wheelSpins') !== undefined) {
                refetchPlayerState()
              } else {
                numeralRewards.forEach(r => updatePlayerState(r.type, r.value))
              }
            }
            resolve()
          })
        })

      const lootboxPromise = () =>
        new Promise<void>(resolve => {
          if (!lootboxRewards.length) {
            resolve()
            return
          }
          Promise.all(
            lootboxRewards.map(r =>
              openLootbox(r.type).then(data => ({
                type: data.lootboxType,
                rewards: data.rewards
              }))
            )
          ).then(lootboxes => {
            lootBoxStore.showReward(lootboxes, resolve)
          })
        })

      const skinPromise = () =>
        new Promise<void>(resolve => {
          if (!skinRewards.length) {
            resolve()
            return
          }
          const totalMultiplier = skinRewards.reduce((acc, r) => acc + r.multiplier, 0)
          skinStore.showReward(
            skinRewards.map((r, index) => ({
              skinId: r.value,
              multiplier: skinStore.getMultiplier(
                index,
                playerState.value!.multiplier ?? 1,
                skinRewards
              ),
              plusMultiplier: r.multiplier
            })),
            async () => {
              if (options.shouldUpdatePlayerState) {
                updatePlayerState('multiplier', totalMultiplier)
              }
              resolve()
              await checkSkinsAchievement()
            }
          )
        })

      numericPromise()
        .then(() => lootboxPromise())
        .then(() => skinPromise())
        .then(() => resolve())
    })
  }

  const defaultRewardOptions = {
    isAlreadyOnPlayerState: false,
    image: ''
  }

  /**
   * @param reward RewardInfo
   * @param options substractFromPlayerState - set to true when reward is already on player balance before showing
   * @returns Promise<void>
   */
  const showReward = (
    reward: RewardInfo,
    options: { isAlreadyOnPlayerState?: boolean; image?: string } = defaultRewardOptions
  ) => {
    const finalOptions = { ...defaultRewardOptions, ...options }
    return new Promise<void>(resolve => {
      if (isNumeralRewardType(reward.type)) {
        const value = getCurrencyRealAmount(reward.value, reward.type)
        const prevValue = getCurrentValue(reward.type, playerState.value)
        rewardStore.showReward(
          [
            {
              type: reward.type,
              value,
              prevValue: finalOptions.isAlreadyOnPlayerState ? prevValue - value : prevValue
            }
          ],
          async () => {
            if (!finalOptions.isAlreadyOnPlayerState) {
              if (reward.type === 'wheelSpins') {
                await refetchPlayerState()
              } else if (isNumeralRewardType(reward.type)) {
                updatePlayerState(reward.type, reward.value)
              }
            }
            resolve()
          },
          finalOptions.image
        )
      } else if (isTimeRewardType(reward.type)) {
        rewardStore.showReward(
          [
            {
              type: reward.type,
              duration: reward.value
            }
          ],
          async () => {
            await refetchPlayerState()
            resolve()
          }
        )
      } else if (isSimpleRewardType(reward.type)) {
        rewardStore.showReward([{ type: reward.type }], async () => {
          if (!finalOptions.isAlreadyOnPlayerState) {
            updatePlayerState('lives', playerState.value!.livesMax)
          }
          resolve()
        })
      } else if (isLootboxRewardType(reward.type)) {
        openLootbox(reward.type).then(data => {
          lootBoxStore.showReward(
            [
              {
                type: data.lootboxType,
                rewards: data.rewards
              }
            ],
            resolve
          )
        })
      } else if (isSkinRewardType(reward.type)) {
        const currentMultiplier = playerState.value!.multiplier ?? 1
        const plusMultiplier = reward.multiplier ?? 1
        const multiplier = finalOptions.isAlreadyOnPlayerState
          ? currentMultiplier - plusMultiplier
          : currentMultiplier
        skinStore.showReward(
          [
            {
              skinId: reward.value,
              multiplier,
              plusMultiplier: plusMultiplier
            }
          ],
          async () => {
            if (!finalOptions.isAlreadyOnPlayerState) {
              updatePlayerState('multiplier', reward.multiplier ?? 1)
            }
            resolve()
            await checkSkinsAchievement()
          }
        )
      } else {
        resolve()
      }
    })
  }

  return { showRewards, showReward }
}
