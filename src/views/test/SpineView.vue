<script lang="ts" setup>
import * as spine from '@esotericsoftware/spine-player'
import '@esotericsoftware/spine-player/dist/spine-player.min.css'
import { onMounted, ref } from 'vue';

const spinePlayer = ref<spine.SpinePlayer | null>(null);
const currentAnimation = ref('t5_IDLE');

onMounted(() => {
  new spine.SpinePlayer('player-container', {
    skeleton: '/spine/Revive_Portal.json',
    atlas: '/spine/Revive_Portal.atlas.txt',
    scale: 3,
    preserveDrawingBuffer: true,
    animation: 't5_IDLE',
    animations: ['t5_Rainbow_Appear', 't5_IDLE'],
    showControls: false,
    interactive: false,
    backgroundColor: '#00000000',
    alpha: true,
    showLoading: false,
    success: function (player) {
      spinePlayer.value = player
    },
    error: function (player, reason) {
        alert(reason);
    }
  })
})

const onClick = () => {
  if (spinePlayer.value) {
    console.log('CLICK')
    currentAnimation.value = currentAnimation.value === 't5_IDLE' ? 't5_Rainbow_Appear' : 't5_IDLE';
    spinePlayer.value.setAnimation(currentAnimation.value, true);
  }
}
</script>

<template>
  <div
    id="player-container"
    class="w-full h-full"
    @click="onClick"
  ></div>
</template>

<style lang="scss">
</style>
