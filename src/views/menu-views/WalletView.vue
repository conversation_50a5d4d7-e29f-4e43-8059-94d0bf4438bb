<script setup lang="ts">
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'

import VButton from '@/components/UI/VButton.vue'

import copyIcon from '@/assets/images/temp/copy.svg'
import historyIcon from '@/assets/images/temp/history.svg'
import unlinkIcon from '@/assets/images/temp/unlink.svg'

import LoaderText from '@/components/LoaderText.vue'
import FeatureBlocker from '@/components/UI/FeatureBlocker.vue'
import DisconnectWallet from '@/components/wallet/DisconnectWallet.vue'
import TransactionsHistory from '@/components/wallet/TransactionsHistory.vue'
import WithdrawForm from '@/components/wallet/WithdrawForm.vue'
import {
  useIsWalletConnected,
  useWallectDisconnection,
  useWalletConnection
} from '@/composables/useWallet'
import { CURRENCY_IMAGES, CURRENCY_NAMES, getCurrencyRealAmount } from '@/constants/currency'
import { useAssetsList } from '@/services/client/useAssets'
import { hapticsService } from '@/shared/haptics/hapticsService.ts'
import { useToast } from '@/stores/toastStore'
import { copyToClipboard } from '@/utils/clipboard'
import { customTruncate, formatNumberToShortString, formatNumberWithSeparator } from '@/utils/number.ts'
import { truncateString } from '@/utils/string'
import MissionItem from '@/components/missions/MissionItem.vue'
import { MISSIONS_IMAGES, type FeatureMissions } from '@/constants/feature-missions'
import { useClaimWithdrawTask } from '@/services/client/useClaimWithdrawTask'
import { useReward } from '@/composables/useReward'

const { t } = useI18n()
const { showToast } = useToast()

const { isWalletConnectedOnServer, walletAddress } = useIsWalletConnected()
const { connectWallet } = useWalletConnection()
const { disconnectWallet } = useWallectDisconnection()
const {
  assets,
  balance,
  isLoading: isLoadingAssets,
  tasks,
  areTasksCompleted,
  tasksLeft
} = useAssetsList()
const { claimWithdrawTask: claimWithdrawTaskMutation } = useClaimWithdrawTask()
const { showReward } = useReward()

const claimWithdrawTask = (id: number) => {
  const task = tasks.value.find(task => task.id === id)
  if (!task || !task.isDone || task.isClaimed) return
  claimWithdrawTaskMutation(id).then(() => {
    showReward(task.reward)
  })
}

const isOpenDisconnectWallet = ref(false)

const copyWalletAddress = () => {
  copyToClipboard(walletAddress.value).then(() => {
    showToast('Address copied', 'info')
    hapticsService.triggerNotificationHapticEvent('success')
  })
}

const selectedAssetCurrency = ref<string>('ton')
const selectedAsset = computed(() => {
  return (
    assets.value.find(asset => asset.currency === selectedAssetCurrency.value) || assets.value[0]
  )
})
const isEnoughtToWithdraw = computed(() => {
  if (!selectedAsset.value) return false
  return selectedAsset.value.amount >= selectedAsset.value.withdrawMin
})

const isOpenWithdraw = ref(false)
const openWidthdraw = () => {
  isOpenWithdraw.value = true
}
const closeWithdraw = () => {
  isOpenWithdraw.value = false
}

const isOpenTransactionHistory = ref(false)
const openTransactionHistory = () => {
  isOpenTransactionHistory.value = true
}
const closeTransactionHistory = () => {
  isOpenTransactionHistory.value = false
}
</script>

<template>
  <div class="wallet h-full flex flex-col">
    <div class="shrink-0 wallet__balance relative">
      <p class="text-center text-[30px] leading-[40px] text-shadow">
        {{ isLoadingAssets ? '...' : '$' + balance }}
      </p>
      <MissionItem
        v-for="item in tasks"
        class="mb-[7px]"
        :key="item.id"
        :id="item.id"
        :image="MISSIONS_IMAGES[item.name as FeatureMissions] ?? ''"
        :description="
          t(
            `wallet.missions.${item.name}`,
            { target: formatNumberToShortString(item.requiredValue) }
          )
        "
        :reward="item.reward"
        :is-done="item.isDone"
        :is-collected="item.isClaimed"
        :progress="{
          current: item.currentValue,
          goal: item.requiredValue
        }"
        @click="claimWithdrawTask"
      />
      <p v-if="!isWalletConnectedOnServer" class="text-center text-[16px] leading-[32px] text-[#1E4073] mt-[7px]">
        {{ t('wallet.connectWallet') }}
      </p>
      <p v-else-if="!selectedAsset" class="text-center text-[16px] leading-[32px] text-[#1E4073] mt-[7px]">
        {{ t('wallet.selectAsset') }}
      </p>
      <FeatureBlocker
        v-else
        class="!absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2 w-[156px]"
        feature="withdraw"
        placement="bottom"
        :is-feature-blocked="!areTasksCompleted"
      >
        <template #default>
          <VButton
            class="!w-full"
            :text="t('actions.withdraw')"
            :disabled="!isEnoughtToWithdraw"
            size="small"
            @click="openWidthdraw"
          />
        </template>
        <template #tooltip-content>
          <i18n-t
            class="text-[13px] leading-[17px] text-[#1E4073] text-center whitespace-pre"
            tag="p"
            keypath="blockers.default"
          >
            <template v-slot:target>
              <span class="text-[#FF8C00]">
                {{ t('blockers.goal', { target: tasksLeft }, tasksLeft) }}
              </span>
            </template>
            <template v-slot:feature>
              <span class="text-[#FF8C00]">
                {{ t('features.withdraw') }}
              </span>
            </template>
          </i18n-t>
        </template>
      </FeatureBlocker>
      <img
        class="w-[22px] absolute top-[10px] right-[10px]"
        :src="historyIcon"
        @click="openTransactionHistory"
        alt="history"
      />
    </div>
    <p class="shrink-0 text-[16px] leading-[22px] text-[#6DB0ED] pb-[10px] pt-[16px]">
      {{ t('wallet.assets') }}
    </p>
    <div class="flex-1 wallet__assets-list space-y-[6px]">
      <LoaderText :isLoading="isLoadingAssets" class="text-center mt-4" />
      <div
        v-for="(asset, index) in assets"
        :key="index"
        class="asset"
        @click="() => (selectedAssetCurrency = asset.currency)"
      >
        <img class="asset__image" :src="CURRENCY_IMAGES[asset.currency]" alt="currency" />
        <div class="asset__info">
          <div class="asset__info-line">
            <p class="text-[20px] font-extrabold uppercase">
              {{ CURRENCY_NAMES[asset.currency] }}
            </p>
            <p
              class="text-[16px] text-shadow"
              :class="{ 'text-[#7AF500]': asset.currency === 'ton' && isEnoughtToWithdraw }"
            >
              {{
                formatNumberWithSeparator(
                  customTruncate(getCurrencyRealAmount(asset.amount, asset.currency))
                )
              }}
              <span v-if="asset.currency === 'ton'">/</span>
              <span v-if="asset.currency === 'ton'" class="text-[#7AF500]">
                {{ tasks.find(t => t.name === 'ton_amount')?.requiredValue ?? 0 }}
              </span>
            </p>
          </div>
          <div class="asset__info-line text-[14px] text-[#6DB0ED] font-bold uppercase">
            <p>
              {{ asset.currency }}
            </p>
            <p>${{ asset.usd }}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="shrink-0 wallet__buttons">
      <VButton
        v-if="isWalletConnectedOnServer"
        type="accent"
        :image="unlinkIcon"
        @click="isOpenDisconnectWallet = true"
      />
      <VButton
        v-if="isWalletConnectedOnServer"
        type="success"
        :text="truncateString(walletAddress, 12)"
        :image="copyIcon"
        image-to-right
        class="flex-1"
        @click="copyWalletAddress"
      />
      <VButton
        v-else
        type="success"
        class="flex-1"
        :text="t('actions.connectWallet')"
        @click="connectWallet"
      />
    </div>
    <TransactionsHistory :isOpen="isOpenTransactionHistory" @close="closeTransactionHistory" />
    <WithdrawForm
      v-if="selectedAsset"
      :isOpen="isOpenWithdraw"
      :walletAddress="walletAddress"
      :currency="selectedAsset.currency"
      :balance="getCurrencyRealAmount(selectedAsset.amount, selectedAsset.currency)"
      :minAmount="getCurrencyRealAmount(selectedAsset.withdrawMin, selectedAsset.currency)"
      @close="closeWithdraw"
    />
    <DisconnectWallet
      :is-open="isOpenDisconnectWallet"
      @close="isOpenDisconnectWallet = false"
      @confirm="disconnectWallet"
    />
  </div>
</template>

<style lang="scss">
.wallet {
  padding: 22px 20px 24px;

  &__balance {
    width: 100%;
    background-color: #00eeff33;
    border-radius: 9px;
    padding: 16px 7px 25px;
  }

  &__assets-list {
    overflow-y: auto;
    overflow-x: visible;
  }

  &__buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
  }

  .mission-wrapper_collected {
    opacity: 1;
  }
}

.asset {
  display: flex;
  align-items: center;
  column-gap: 9px;

  padding: 6px 16px 6px 6px;
  border-radius: 5px;
  background-color: #0f4589;

  &__image {
    width: 35px;
    height: 35px;
  }

  &__info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__info-line {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
