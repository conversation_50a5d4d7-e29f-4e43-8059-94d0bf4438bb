<script setup lang="ts">
import LoaderText from '@/components/LoaderText.vue';
import SkinCollectionWindow from '@/components/skins/SkinCollectionWindow.vue';
import BalanceItem from '@/components/UI/BalanceItem.vue';
import ProgressBar from '@/components/UI/ProgressBar.vue';
import { useIconImage } from '@/composables/useIconImage';
import { getCurrencyRealAmount } from '@/constants/currency';
import { useSkinCollections } from '@/services/client/useSkinCollections';
import { formatNumberToShortString } from '@/utils/number';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';

const NULL_COLLECTION_ID = -1

const { t } = useI18n()
const { getImageClass } = useIconImage()
const { isLoading, collections } = useSkinCollections()

const collectionInfoId = ref<number>(NULL_COLLECTION_ID)

const collectionInfo = computed(() => {
  if (collectionInfoId.value === NULL_COLLECTION_ID) return null
  return collections.value.find(collection => collection.id === collectionInfoId.value) ?? null
})
</script>

<template>
  <div class="skin-collections">
    <div class="w-full h-full flex justify-center items-center" v-if="isLoading">
      <LoaderText isLoading />
    </div>
    <div class="skin-collections__list">
      <div
        class="skin-collection"
        v-for="collection in collections"
        :key="collection.id"
        @click="collectionInfoId = collection.id"
      >
        <div class="skin-collection__name">
          {{ t(`skins.collections.${collection.id}`) }}
        </div>
        <div class="skin-collection__info">
          <ProgressBar
            class="skin-collection__progress"
            inner-wrapper-class="skin-collection__progress-inner-wrapper"
            :progress="collection.ownedSkinsCount"
            :goal="collection.totalSkinsCount"
          >
            <p class="text-shadow text-shadow_black text-shadow_thin">
              {{ collection.ownedSkinsCount }}/{{ collection.totalSkinsCount }}
            </p>
          </ProgressBar>
          <div
            v-if="!collection.isClaimed"
            class="skin-collection__reward-container !left-auto"
          >
            <BalanceItem
              class="skin-collection__reward"
              image-class="skin-collection__reward-image"
              :iconName="getImageClass(collection.reward.type)"
              barClass="skin-collection__reward-bar"
              balanceClass="skin-collection__reward-balance"
            >
              {{
                formatNumberToShortString(
                  getCurrencyRealAmount(collection.reward.value, collection.reward.type)
                )
              }}
            </BalanceItem>
          </div>
        </div>
      </div>
    </div>
    <SkinCollectionWindow
      :collection="collectionInfo"
      @close="collectionInfoId = NULL_COLLECTION_ID"
    />
  </div>
</template>

<style lang="scss">
.skin-collections {
  min-height: 100%;
  width: inherit;
  padding: 22px 20px;

  &__list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .skin-collection {
    position: relative;
    aspect-ratio: 1 / 1.5;
    background-color: #D9D9D94D;
    border-radius: 8px;

    &__name {
      white-space: nowrap;
      font-size: 16px;
      position: absolute;
      top: 0;
      left: 50%;
      transform: translate(-50%, 50%);
    }

    &__info {
      width: 90%;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translate(-50%, -50%);

      display: flex;
    }

    &__progress {
      flex: 1;
      height: 22px;

      &-inner-wrapper {
        padding: 2px;
      }
    }

    &__reward {
      &-image {
        width: 28px;
        height: 28px;

        &-heart {
          left: -3px;
        }
      }

      &-bar {
        height: 22px;
        padding-left: 17px;
        padding-right: 8px;
      }

      &-balance {
        font-size: 15px;
      }
    }
  }
}
</style>
