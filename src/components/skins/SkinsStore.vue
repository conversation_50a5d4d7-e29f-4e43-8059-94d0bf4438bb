<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import lockImage from '@/assets/images/temp/lock.svg'
import {
  DEFAULT_SKIN_ID,
  SKIN_ID_TO_IMAGE,
  SKINS_RARITY,
} from '@/constants/skins'

import VButton from '../UI/VButton.vue'
import SkinItem from './SkinItem.vue'

import { useIsWalletConnected } from '@/composables/useWallet.ts'
import { getCurrencyRealAmount } from '@/constants/currency.ts'
import { CURRENCY_TO_IMAGE_CLASS } from '@/composables/useIconImage'
import { usePlayerState } from '@/services/client/usePlayerState'
import { usePurchaseSkin, useSelectSkin, useSkinsList } from '@/services/client/useSkins'
import type { Price, Skin } from '@/services/openapi'
import { hapticsService } from '@/shared/haptics/hapticsService.ts'
import { cloudStorageService } from '@/shared/storage/cloudStorageService.ts'
import type { ShopScrollTarget } from '@/types'
import { sendAnalyticsEvent } from '@/utils/analytics'
import { useRoute, useRouter } from 'vue-router'
import TonPurchaseButton from '@/components/TonPurchaseButton.vue'
import { usePurchase } from '@/composables/usePurchase'
import { useReward } from '@/composables/useReward'
import BalanceItem from '@/components/UI/BalanceItem.vue'
import SkinsList from '@/components/skins/SkinsList.vue'
import starParticle from '@/assets/images/temp/star-particle.png'
import { nextTick } from 'vue'

const router = useRouter()
const route = useRoute()
const { t } = useI18n()

const IS_TON_SKIN_PURCHASE_IN_PROGRESS = 'isTonSkinPurchaseInProgressNew'

type SkinListItem = Skin & { image: string }

const { showReward } = useReward()
const { playerState } = usePlayerState()
const { isWalletConnectedEverywhere } = useIsWalletConnected()
const { isLoading: isLoadingSkins, skinsList } = useSkinsList()
const {
  purchaseSkin: purchaseSkinMutation,
  isPending: isPurchasing
} = usePurchaseSkin()
const { purchaseInvoice } = usePurchase()
const { selectSkin } = useSelectSkin()

const purchaseSkin = (skinId: number, multiplier: number) => {
  purchaseSkinMutation(skinId, multiplier).then(() => {
    showReward({
      type: 'skin',
      value: skinId,
      multiplier: multiplier
    }, { isAlreadyOnPlayerState: true })
  })
}

const isTonSkinPurchaseInProgress = ref(false)

const purchaseSkinWithExternalCurrency = (
  itemId: number,
  skinId: number,
  price: Price,
  multiplier: number
) => {
  purchaseInvoice('skin', itemId, skinId, price, multiplier)
    .then(currency => {
      if (currency === 'ton') {
        cloudStorageService.save(IS_TON_SKIN_PURCHASE_IN_PROGRESS, true)
        isTonSkinPurchaseInProgress.value = true
      }
    })
}

const checkTonSkinProgress = () => {
  cloudStorageService.load<boolean>(IS_TON_SKIN_PURCHASE_IN_PROGRESS).then(inProgress => {
    isTonSkinPurchaseInProgress.value = !!inProgress
  })
}

const playerSkinId = computed(() => playerState.value!.skin ?? DEFAULT_SKIN_ID)
const skinFromRouteQuery = route.query.skin && +route.query.skin
const previewedSkinRef = ref(skinFromRouteQuery || playerState.value!.skin!)
const skinFilter = ref<'all' | 'owned' | 'not_owned'>('all')
const isTransitionActive = ref<boolean>(false)

const setSkinFilter = (currentFiler: 'all' | 'owned' | 'not_owned') => {
  if (currentFiler === 'all') {
    skinFilter.value = 'owned'
  } else if (currentFiler === 'owned') {
    skinFilter.value = 'not_owned'
  } else {
    skinFilter.value = 'all'
  }
  isTransitionActive.value = true
  setTimeout(() => {
    isTransitionActive.value = false
  }, 200)
}

const defaultPlayerSkin: SkinListItem = {
  id: DEFAULT_SKIN_ID,
  locked: false,
  multiplier: 0,
  purchased: true,
  requiresRefs: 0,
  rarity: 'common',
  requiresTransaction: false,
  requiresWallet: false,
  requiresDailyReward: false,
  image: SKIN_ID_TO_IMAGE[DEFAULT_SKIN_ID],
}

const finalSkinsList = computed<SkinListItem[]>(() => {
  const array: SkinListItem[] = Array.from(skinsList.value, skin => {
    return {
      ...skin,
      image: SKIN_ID_TO_IMAGE[skin.id],
    }
  }).filter(skin => !!SKIN_ID_TO_IMAGE[skin.id])

  array.push(defaultPlayerSkin)

  return array.sort((a, b) => {
    if (a.rarity === b.rarity && a.purchased === b.purchased) {
      return a.id - b.id
    }
    if (a.purchased === b.purchased) {
      return SKINS_RARITY[a.rarity] - SKINS_RARITY[b.rarity]
    }
    return a.purchased ? -1 : 1
  })
})

const purchasedSkinsAmount = computed(
  () => finalSkinsList.value.filter(skin => skin.purchased).length
)
const previewedSkin = computed<SkinListItem>(() => {
  if (isLoadingSkins.value) {
    return {
      ...defaultPlayerSkin,
      id: playerSkinId.value
    }
  }
  return finalSkinsList.value.find(skin => skin.id === previewedSkinRef.value) || defaultPlayerSkin
})
const isPreviewedSkinSelected = computed(() => playerSkinId.value === previewedSkin.value.id)

const requirenment = computed(() => {
  if (previewedSkin.value.requiresRefs) {
    return t('skins.requirenments.inviteFriend', {
      amount: previewedSkin.value.requiresRefs - (playerState.value!.refs ?? 0)
    })
  }
  if (previewedSkin.value.requiresTransaction) {
    return t('skins.requirenments.transaction')
  }
  if (previewedSkin.value.requiresWallet) {
    return t('skins.requirenments.wallet')
  }
  if (previewedSkin.value.requiresBox) {
    return t('skins.requirenments.box')
  }
  if (previewedSkin.value.requiresDailyReward) {
    return t('skins.requirenments.dailyReward', {
      days: 28 - (playerState.value!.dailyRewards?.currentDay || 1)
    })
  }
  return ''
})


const onSkinClick = (id: number) => {
  hapticsService.triggerImpactHapticEvent('light')
  previewSkin(id)
}

const previewSkin = (id: number) => {
  previewedSkinRef.value = id
}

const goToShop = (target: ShopScrollTarget) => {
  sendAnalyticsEvent('go_to_shop', { from: 'skin-store' })
  router.push({ name: 'shop', query: { scrollTo: target } })
}

const skinNameContainerWidth = ref(0)
const skinNameWidth = ref(0)

const getSkinNameWidth = () => {
  const skinNameContainer = document.querySelector('.skin-preview__name-container')
  const skinName = document.querySelector('.skin-preview__name')
  if (!skinNameContainer || !skinName) return 0
  skinNameContainerWidth.value = skinNameContainer.getBoundingClientRect().width
  skinNameWidth.value = skinName.getBoundingClientRect().width
}

watch(previewedSkin, () => nextTick(getSkinNameWidth))

onMounted(() => {
  checkTonSkinProgress()
  getSkinNameWidth()
})
</script>

<template>
  <div class="skin-store flex flex-col">
    <div class="skin-preview">
      <div
        class="skin-preview__image"
        :class="`skin-preview__image_${previewedSkin.rarity}`"
      >
        <SkinItem
          class="w-full"
          animated
          :src="previewedSkin.image"
          :locked="previewedSkin.locked"
        />
        <div class="skin-preview__particles skin-preview__particles_left">
          <img class="skin-preview__particle" v-for="i in 5" :key="i" :src="starParticle" />
        </div>
        <div class="skin-preview__particles skin-preview__particles_right">
          <img class="skin-preview__particle" v-for="i in 5" :key="i" :src="starParticle" />
        </div>
        <img class="skin-preview__bg skin-preview__bg_green" src="@/assets/images/temp/skin-store/green-bg.png" />
        <img class="skin-preview__bg skin-preview__bg_blue" src="@/assets/images/temp/skin-store/blue-bg.png" />
        <img class="skin-preview__bg skin-preview__bg_pink" src="@/assets/images/temp/skin-store/pink-bg.png" />
        <img class="skin-preview__bg skin-preview__bg_yellow" src="@/assets/images/temp/skin-store/yellow-bg.png" />
      </div>
      <BalanceItem
        class="ml-3 mb-1"
        image-class="-top-[12px] !w-[39px] !h-[39px]"
        bar-class="!pr-[10px]"
        balance-class="!text-[16px] font-black text-[#FF9205]"
        iconName="multiplier-tickets-bg"
      >
        {{ previewedSkin.multiplier }}
      </BalanceItem>
      <div class="skin-preview__info">
        <div
          class="skin-preview__name-container"
          :style="{
            '--skin-container-width': `${skinNameContainerWidth}px`,
            '--skin-name-width': `${skinNameWidth}px`
          }"
        >
          <p class="skin-preview__name"
            :style="{ 'animation': skinNameWidth > skinNameContainerWidth ? '' : 'none' }"
          >
            {{ t(`skins.list.${previewedSkin.id}`) }}
          </p>
        </div>
        <div class="skin-preview__button">
          <p v-if="previewedSkin.locked" class="text-[13px] text-[#6DB0ED] font-extrabold z-10">
            {{ requirenment }}
            <img :src="lockImage" class="w-[20px] absolute -top-[9px] -right-[8px]" alt="lock" />
          </p>
          <p
            v-else-if="isPreviewedSkinSelected"
            class="text-[20px] text-[#6DB0ED] font-extrabold z-10"
          >
            {{ t('selected') }}
          </p>
          <VButton
            class="!w-full"
            v-else-if="previewedSkin.requiresBox && !previewedSkin.purchased"
            type="success"
            size="medium"
            :text="requirenment"
            @click="() => goToShop('lootbox')"
          />
          <TonPurchaseButton
            v-else-if="!previewedSkin.purchased && previewedSkin.price?.currency === 'ton'"
            class="!w-full"
            :class="{ '!text-[18px]': !isWalletConnectedEverywhere }"
            type="success"
            size="medium"
            :text="getCurrencyRealAmount(previewedSkin.price.amount, 'ton')"
            :image-class="CURRENCY_TO_IMAGE_CLASS.ton"
            :isTransactionInProgress="isTonSkinPurchaseInProgress"
            @purchase="() =>
              purchaseSkinWithExternalCurrency(
                +previewedSkin.shopItem!,
                previewedSkin.id,
                previewedSkin.price!,
                previewedSkin.multiplier
              )
            "
          />
          <VButton
            class="!w-full"
            v-else
            type="success"
            size="medium"
            :text="previewedSkin.purchased ? t('actions.select') : t('free')"
            :disabled="isPurchasing || isPreviewedSkinSelected"
            @click="() => previewedSkin.purchased
              ? selectSkin(previewedSkin.id)
              : purchaseSkin(previewedSkin.id, previewedSkin.multiplier)"
          />
        </div>
      </div>
      <p class="flex justify-end items-center gap-x-2 my-2">
        <span class="text-[20px] text-[#FFB61B]">
          {{ purchasedSkinsAmount }}/{{ finalSkinsList.length }}
        </span>
        <VButton
          type="accent"
          size="xsmall"
          :disabled="isTransitionActive"
          @click="() => setSkinFilter(skinFilter)"
        >
          <template #content>
            <span class="text-[#8F3B00]">{{ t('skins.filter.' + skinFilter) }}</span>
          </template>
        </VButton>
      </p>
    </div>
    <SkinsList
      class="flex-1"
      :columns="4"
      :is-loading="isLoadingSkins"
      :skins="finalSkinsList.slice().filter(skin => {
        if (isTransitionActive) {
          return false
        } else if (skinFilter === 'all') {
          return true
        } else {
          return skinFilter === 'owned' ? skin.purchased : !skin.purchased
        }
      })"
      :player-skin-id="playerSkinId"
      :active-skin-id="previewedSkin.id"
      selectable
      @click="(id) => onSkinClick(id)"
    />
  </div>
</template>

<style lang="scss" scoped>
.skin-store {
  height: 100%;
  overflow: visible;
  padding: 22px 20px 0px;
  display: flex;
  flex-direction: column;
}

.skin-preview {
  &__image {
    position: relative;
    width: 35%;
    margin: 0 auto;
    padding-top: 20px;

    &:after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      background: var(--skin-background);
    }

    .skin-preview__bg {
      position: absolute;
      width: 300%;
      top: -35%;
      left: 50%;
      transform: translateX(-50%);
      opacity: 0;
      transition: opacity 0.3s;
      pointer-events: none;
      z-index: 0;
    }

    .skin {
      --skin-item-shadow-height: 8%;
    }

    &_common {
      .skin {
        --skin-item-shadow-bg: #4CAF5033;
      }
      .skin-preview__bg_green {
        opacity: 1;
      }
    }
    &_rare {
      .skin {
        --skin-item-shadow-bg: #1E3A8A33;
      }
      .skin-preview__bg_blue {
        opacity: 1;
      }
    }

    &_epic {
      .skin {
        --skin-item-shadow-bg: #7E22CE33;
      }
      .skin-preview__bg_pink {
        opacity: 1;
      }
    }

    &_legendary {
      .skin {
        --skin-item-shadow-bg: #B4530933;
      }
      .skin-preview__bg_yellow {
        opacity: 1;
      }
    }
  }

  &__info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    column-gap: 20px;
    justify-content: space-between;
    padding: 4px 8px 4px 16px;
    background-color: #00000033;
    border-radius: 15px;
    white-space: nowrap;
  }

  &__name-container {
    position: relative;
    height: 24px;
    overflow: hidden;
    --skin-container-width: 0;
    --skin-name-width: 100%;
  }

  &__name {
    font-size: 24px;
    white-space: nowrap;
    position: absolute;
    left: 0;
    animation: text-move 3s linear 1s infinite forwards;

    @keyframes text-move {
      0%,
      10%,
      90%,
      100% {
        transform: translateX(0px);
      }
      40%,
      60% {
        transform: translateX(calc(-1 * max(0px, calc(var(--skin-name-width) - var(--skin-container-width)))));
      }
    }
  }

  &__button {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 42px;

    &::before {
      transform: skew(-10deg);
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      border-radius: 6px;
      width: 100%;
      height: 100%;
      background-color: #0826739c;
      box-shadow: inset #00000012 0 -12px;
    }
  }

  &__particles {
    position: absolute;
    top: 0;
    width: 80%;
    height: 90%;
    z-index: 1;

    &_left {
      transform: translateX(-100%);
      left: 0;
    }
    &_right {
      transform: translateX(100%);
      right: 0;
    }
  }

  &__particle {
    position: absolute;
    width: 50px;
    opacity: 0;
    animation: particle-opacity 3s linear infinite, particle-random-move 3s linear infinite;
  }

  &__particles_left {
    .skin-preview__particle {
      &:nth-child(1) {
        top: 0;
        left: 40%;
        width: 15px;
        animation-delay: 0s;
        animation-duration: 2s;
      }
      &:nth-child(2) {
        top: 30%;
        left: 70%;
        animation-delay: 0.5s;
        animation-duration: 3s;
      }
      &:nth-child(3) {
        top: 50%;
        left: 0;
        width: 20px;
        animation-delay: 0.3s;
        animation-duration: 1.5s;
      }
      &:nth-child(4) {
        top: 70%;
        left: 55%;
        width: 15px;
        animation-delay: 1s;
        animation-duration: 3s;
      }
      &:nth-child(5) {
        bottom: 0;
        left: 20%;
        width: 13px;
        animation-delay: 0.4s;
        animation-duration: 2s;
      }
    }
  }

  &__particles_right {
    .skin-preview__particle {
      &:nth-child(1) {
        top: 5%;
        left: 30%;
        width: 15px;
        animation-delay: 0.4s;
        animation-duration: 2s;
      }
      &:nth-child(2) {
        top: 30%;
        right: 0;
        width: 25px;
        animation-delay: 1s;
        animation-duration: 4s;
      }
      &:nth-child(3) {
        top: 40%;
        left: 0;
        width: 20px;
        animation-delay: 0.6s;
        animation-duration: 1.5s;
      }
      &:nth-child(4) {
        top: 55%;
        left: 50%;
        animation-delay: 0.3s;
        animation-duration: 3s;
      }
      &:nth-child(5) {
        bottom: 0;
        left: 20%;
        width: 13px;
        animation-delay: 0.1s;
        animation-duration: 1.5s;
      }
    }
  }

  @keyframes particle-opacity {
    0%, 90%, 100% {
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
  }

  @keyframes particle-random-move {
    0% {
      transform: translateY(0);
    }
    100% {
      transform: translateY(100%);
    }
  }
}
</style>
