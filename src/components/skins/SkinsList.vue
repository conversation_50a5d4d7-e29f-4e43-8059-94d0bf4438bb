<script setup lang="ts">
import { SKIN_ID_TO_IMAGE } from '@/constants/skins';
import SkinItem from './SkinItem.vue';
import LoaderText from '@/components/LoaderText.vue';
import { useI18n } from 'vue-i18n';
import type { Skin } from '@/services/openapi';
import checkIcon from '@/assets/images/temp/check-icon.png'
import { useHasUserSeenSkins } from '@/stores/hasUserSeenStore';
import NewBadge from '@/components/UI/NewBadge.vue';

const { t } = useI18n()

const props = defineProps<{
  skins: Skin[]
  columns: number
  isLoading?: boolean,
  playerSkinId?: number
  activeSkinId?: number
  showNewBadge?: boolean
  showName?: boolean
  selectable?: boolean
  hideNotOwned?: boolean
}>()
const emit = defineEmits(['click'])

const hasUserSeenStore = useHasUserSeenSkins()

const onClick = (id: number) => {
  if (!props.selectable) return
  if (hasUserSeenStore.newSkins.includes(id)) {
    hasUserSeenStore.markSkinAsSeen(id)
  }
  emit('click', id)
}
</script>

<template>
  <div class="skin-list" :style="{ gridTemplateColumns: `repeat(${columns}, minmax(0, 1fr))` }">
    <TransitionGroup name="skin-list">
      <div
        v-for="skin in skins"
        :key="skin.id"
        class="skin-list__item-wrapper"
        :class="`
          skin-list__item-wrapper_style-${skin.rarity}
          skin-list__item-wrapper_purchased-${skin.purchased}
        `"
        @click="() => onClick(skin.id)"
      >
        <NewBadge
          v-if="showNewBadge && hasUserSeenStore.newSkins.includes(skin.id)"
          class="-top-[5px] -left-[1px] z-20"
          size="large"
        />
        <div
          class="skin-list__item skin-list__item_style"
          :class="{
            'skin-list__item_active': skin.id === activeSkinId
          }"
        >
          <SkinItem
            v-if="!hideNotOwned || skin.purchased"
            class="skin-list__skin"
            :class="{'skin-list__skin_with-name': showName}"
            :src="SKIN_ID_TO_IMAGE[skin.id]"
          />
          <div class="skin-list__item-name truncate" v-if="showName">
            {{ t(`skins.list.${skin.id}`) }}
          </div>
          <img
            class="skin-list__item_selected"
            v-if="playerSkinId && playerSkinId === skin.id"
            :src="checkIcon"
            alt="selected check"
          />
        </div>
      </div>
    </TransitionGroup>
    <LoaderText
      v-if="isLoading"
      class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
      isLoading
    />
  </div>
</template>

<style lang="scss">
.skin-list {
  display: grid;
  grid-template-rows: min-content;
  gap: 12px;
  padding: 16px;
  background-color: #0000001A;
  border-radius: 8px;
  
  overflow-y: auto;
  overflow-x: visible;

  .skin-list__skin {
    position: relative;
    top: -4px;
    width: 100%;
    margin: 0 auto;

    &_with-name {
      top: -8px;
      width: 85%;
    }
  }

  &__item-wrapper {
    position: relative;
    aspect-ratio: 1;

    &_purchased-false {
      opacity: 0.5;
    }

    &_style {
      &-common {
        --skin-border-color: #91ff70;
        --skin-background: linear-gradient(180deg, rgba(145, 255, 112, 0.32) 0%, #54de2a 100%);
        --decorative-color: #ccff7f;
        --skin-name-color: #86dd03;
      }

      &-rare {
        --skin-border-color: #7bd3ff;
        --skin-background: linear-gradient(180deg, rgba(14, 206, 246, 0.3) 0%, #00a6ff 100%);
        --decorative-color: #9ff9ff;
        --skin-name-color: #0084BD;
      }

      &-epic {
        --skin-border-color: #ff7ef3;
        --skin-background: linear-gradient(180deg, rgba(255, 44, 227, 0.3) 0%, #ff2ce3 100%);
        --decorative-color: #ffb9e7;
        --skin-name-color: #B8049F;
      }

      &-legendary {
        --skin-border-color: #FFE800;
        --skin-background: linear-gradient(180deg, rgba(255, 234, 0, 0.3) 0%, #FFB800 100%);
        --decorative-color: #fdf16c;
        --skin-name-color: #A28000;
      }
    }

    &::before {
      content: '';
      position: absolute;
      width: 36px;
      height: 4px;
      top: 0;
      right: 20%;
      background: linear-gradient(to right, var(--decorative-color) 60%, white 60%);
      z-index: 2;
    }

    &::after {
      content: '';
      position: absolute;
      width: 36px;
      height: 4px;
      bottom: 0;
      left: 20%;
      background: linear-gradient(to right, white 40%, var(--decorative-color) 40%);
      z-index: 2;
    }
  }

  &__item {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 2px 0 10px 0;
    z-index: 1;

    background: var(--skin-background);
    border: 4px solid var(--skin-border-color);
    border-radius: 8px;

    &_active {
      outline: 3px solid white;

      &::before {
        content: '';
        position: absolute;
        top: -13px;
        left: 0;
        width: 100%;
        height: 20px;
        background: radial-gradient(50% 50% at 50% 50%, #ffffff 17.5%, rgba(255, 255, 255, 0) 100%);
        z-index: 1;
        opacity: 0.7;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: -13px;
        left: 0;
        width: 100%;
        height: 20px;
        background: radial-gradient(50% 50% at 50% 50%, #ffffff 17.5%, rgba(255, 255, 255, 0) 100%);
        z-index: 1;
        opacity: 0.7;
      }
    }

    &_purchased {
      width: 15px;
      position: absolute;
      top: 2px;
      left: 2px;
    }

    &_selected {
      width: 28px;
      position: absolute;
      top: -8px;
      left: -4px;
    }
  }
  
  &__item-name {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 17px;
    background-color: var(--skin-name-color);
    border-radius: 0 0 6px 6px;
    padding: 0 2px;

    font-size: 14px;
    line-height: 17px;
    text-align: center;
  }

  .skin-list-move,
  .skin-list-enter-active,
  .skin-list-leave-active {
    transition: all 0.2s ease;
  }
  
  .skin-list-enter-from,
  .skin-list-leave-to {
    opacity: 0;
    transform: scale(0.5);
  }
}
</style>
