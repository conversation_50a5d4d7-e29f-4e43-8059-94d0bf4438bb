<script setup lang="ts">
import { usePlayerState } from '@/services/client/usePlayerState'
import { customTruncate, formatNumberToShortString, formatNumberWithSeparator } from '@/utils/number'
import { onBackButtonClick } from '@telegram-apps/sdk'
import { computed, onMounted, onUnmounted, ref, useTemplateRef } from 'vue'
import { useI18n } from 'vue-i18n'
import VButton from '../UI/VButton.vue'

import clockImage from '@/assets/images/temp/clock.png'
//import eventCoinImage from '@/assets/images/temp/battle-event/coin.png'
import eventCoinImage from '@/assets/images/temp/custom-coin-event/coin.png'
import exploiters from '@/assets/images/temp/banners/exploiters/exploiters-count.png'
import explosion from '@/assets/images/temp/hotrecord/explosion.png'
import leaf from '@/assets/images/temp/onepercent/leaf.png'
import tonImage from '@/assets/images/temp/big-icons/ton-l.png'

import { getCurrencyRealAmount } from '@/constants/currency.ts'
import { useAchievementsList } from '@/services/client/useAchievements.ts'
import { useGetRevive, usePurchaseRevive } from '@/services/client/useRevive'
import leaguesService from '@/services/local/leagues'
import type { RevivalPrice, ReviveInfoResponse } from '@/services/openapi'
import ModalWindow from '../UI/ModalWindow.vue'
import PurchaseCurrencyWindow from '@/components/shop/PurchaseCurrencyWindow.vue'
import BalanceItem from '../UI/BalanceItem.vue'
import ProgressBar from '../UI/ProgressBar.vue'

const { playerState } = usePlayerState()
const playerLeague = computed(() => playerState.value!.leagueLevel ?? 1)

const TIME_TO_REVIVE = 5_000

const { t } = useI18n()

export type Props = {
  sessionId: number
  score: string
  highScore: string
  highScoreEvent: string
  totalScoreEvent: string
  mobsKillCount: number
  nextTon: {
    height?: string
    amount?: string
  }
  nextEventCoin: {
    height?: string
    amount?: string
  }
}

const props = defineProps<Props>()
const emit = defineEmits(['revive', 'gameOver'])

const reviveInfo = ref<ReviveInfoResponse | null>(null)

const reviveTimeoutRef = ref<any>(null)
const purchaseCurrencyWindowRef = useTemplateRef('purchaseCurrencyWindowRef')

const { getReviveInfo, isPending: isGetRevivePending } = useGetRevive()
const { purchaseRevive, isPending: isPurchasingRevive } = usePurchaseRevive()

// TODO: dont fetch achievements in revive window
const { refetch } = useAchievementsList(false)

getReviveInfo(props.sessionId)
  .then(data => {
    refetch()
    if (data.maxRevivalsAmount === data.usedRevivalsAmount) {
      emit('gameOver')
    } else {
      reviveInfo.value = data
      startTimer()
    }
  })
  .catch(() => {
    emit('gameOver')
  })

const stats = computed(() => ({
  score: +props.score,
  highScore: +props.highScore,
  highScoreEvent: +props.highScoreEvent,
  totalScoreEvent: +props.totalScoreEvent
}))
const isHighScoreEvent = computed(() => stats.value.score > stats.value.highScoreEvent)
const isTotalScoreEvent = computed(() => stats.value.totalScoreEvent > 0)
const mobsLeftToKill = computed(() => {
  const taskMobsKillCount = playerState.value?.dailyWriteOffState?.mobsToKill ?? 0
  return props.mobsKillCount < taskMobsKillCount ? taskMobsKillCount - props.mobsKillCount : 0
})

const startTimer = () => {
  reviveTimeoutRef.value = setTimeout(() => {
    emit('gameOver')
  }, TIME_TO_REVIVE)
}

const stopTimer = () => {
  clearTimeout(reviveTimeoutRef.value)

  // reset clock animation with delay
  setTimeout(() => {
    reviveTimeoutRef.value = null
  }, 500)
}

const skipRevive = () => {
  if (!isPurchasingRevive.value) {
    stopTimer()
    emit('gameOver')
  }
}

const handleReviveHorns = () => {
  stopTimer()
  const playerHorns = playerState.value?.magicHorns ?? 0
  const revivePrice = reviveInfo.value?.prices[0] ?? null
  const offers = reviveInfo.value?.magicHornOffers ?? []
  if (!revivePrice) return
  if (playerHorns < revivePrice.price) {
    purchaseCurrencyWindowRef.value?.openWindow(offers, 'magicHorns', revivePrice.price - playerHorns)
    return
  }
  purchaseRevive(props.sessionId, stats.value.score, revivePrice)
    .then(() => {
      emit('revive')
    })
    .catch(() => {
      startTimer()
    })
}

const handleRevive = () => {
  stopTimer()
  // for free revive should pass any currency
  const defaultPrice: RevivalPrice = { type: 'magicHorns', price: 0 }
  purchaseRevive(props.sessionId, stats.value.score, defaultPrice)
    .then(() => {
      emit('revive')
    })
    .catch(() => {
      startTimer()
    })
}

const purchaseStars = (starsNeed: number) => {
  const offers = reviveInfo.value?.hardOffers ?? []
  purchaseCurrencyWindowRef.value?.openWindow(offers, 'hard', starsNeed)
}

const onPurchased = (type: string) => {
  if (type === 'hard') {
    stopTimer()
    const playerHorns = playerState.value?.magicHorns ?? 0
    const revivePrice = reviveInfo.value?.prices[0]?.price ?? 0
    const offers = reviveInfo.value?.magicHornOffers ?? []
    purchaseCurrencyWindowRef.value?.openWindow(offers, 'magicHorns', revivePrice - playerHorns)
  }
}

const backButtonRemoveListener = ref<Function | null>(null)

const backButtonClicked = () => {
  skipRevive()
}

// for opacity animation if you manage open state with v-if in parent
const isOpen = ref(false)
onMounted(() => {
  setTimeout(() => {
    isOpen.value = true
  })
  if (onBackButtonClick.isSupported()) {
    try {
      backButtonRemoveListener.value = onBackButtonClick(backButtonClicked)
    } catch (e) {
      backButtonClicked()
      console.log(e)
    }
  }
})
onUnmounted(() => {
  stopTimer()
  if (backButtonRemoveListener.value !== null) {
    backButtonRemoveListener.value()
  }
})
</script>

<template>
  <ModalWindow
    :is-open="isOpen"
    class="revive-modal"
    overlay-class="overlay_transparent !px-[30px]"
    hide-close
    :is-loading="isGetRevivePending && !reviveTimeoutRef"
  >
    <div class="absolute -top-[38px] -right-[16px]">
      <img class="w-[86px]" :src="clockImage" alt="clock" />
      <div class="absolute bottom-[15px] left-[15px] clock">
        <div
          class="clock__dial"
          :class="{ clock__dial_active: reviveTimeoutRef !== null }"
          :style="{ '--time': `${TIME_TO_REVIVE}ms` }"
        ></div>
      </div>
    </div>
    <BalanceItem
      class="horn-balance-item"
      iconName="horn-bg"
      balance-class="text-shadow text-shadow_black text-shadow_thin"
    >
      {{ formatNumberToShortString(playerState!.magicHorns ?? 0) }}
    </BalanceItem>
    <h1
      class="text-[32px] leading-[44px] text-[#FFE657] text-shadow text-shadow_black mb-[14px] text-center"
    >
      {{ t('reviveWindow.saveMe') }}
    </h1>
    <div class="w-full space-y-3">
      <!-- Exploiters heist -->
      <div
        v-if="!!playerState!.dailyWriteOffState?.timestampToFail && mobsLeftToKill"
        class="relative flex items-center justify-center"
      >
        <img class="w-full h-[114px]" :src="exploiters" alt="mobs count" />
        <img
          :src="tonImage"
          class="absolute w-[57px] bottom-[40px] left-[46%] -translate-x-1/2"
          alt="tons image"
        />
        <p
          v-if="playerState!.dailyWriteOffState?.tonLockedBalance"
          class="absolute bottom-[50px] left-[110px] text-[16px] leading-[18px] text-white text-shadow text-shadow_black text-shadow_thin"
        >
          {{
            formatNumberWithSeparator(
              customTruncate(
                getCurrencyRealAmount(playerState!.dailyWriteOffState?.tonLockedBalance, 'ton')
              )
            )
          }}
        </p>
        <div
          class="absolute w-full bottom-0 bg-[#32004DB8] rounded-[10px] py-3 text-center box-border"
        >
          <p class="text-[20px] text-shadow text-shadow_yellow text-shadow_thin">
            {{ t('exploiters.count', { count: mobsLeftToKill }) }}
          </p>
        </div>
      </div>
      <!-- Hot Record Event -->
      <div
        v-if="
          !!playerState!.hotrecordEvent && leaguesService.hasAccess(playerLeague, 'hotRecordEvent')
        "
        class="event-score-banner hot-record-highscore"
      >
        <p class="event-score-banner__title">
          {{ t('hotRecord') }}
        </p>
        <p class="event-score-banner__score text-shadow text-shadow_black">
          {{ formatNumberWithSeparator(isHighScoreEvent ? stats.score : stats.highScoreEvent) }}
        </p>
        <img class="hot-record-highscore__image" :src="explosion" alt="explosion" />
        <img class="hot-record-highscore__image" :src="explosion" alt="explosion" />
      </div>
      <!-- Onepercent Event -->
      <div
        v-if="
          !!playerState!.onepercentEvent &&
          leaguesService.hasAccess(playerLeague, 'onePercentEvent')
        "
        class="event-score-banner onepercent-totalscore"
      >
        <p class="event-score-banner__title">
          {{ t('totalScore') }}
        </p>
        <p class="event-score-banner__score text-shadow text-shadow_black">
          {{ isTotalScoreEvent ? formatNumberWithSeparator(stats.totalScoreEvent) : '0' }}
          <span class="text-[#FFD900]">+ {{ formatNumberWithSeparator(stats.score) }}</span>
        </p>
        <img class="onepercent-totalscore__image" :src="leaf" alt="leaf" />
        <img class="onepercent-totalscore__image" :src="leaf" alt="leaf" />
      </div>
      <div
        v-if="nextTon.height && nextTon.amount"
        class="relative h-[79px] bg-[#2397D529] w-full py-[18px] pl-[123px] pr-[14px] rounded-[7px] overflow-hidden"
      >
        <img
          class="absolute w-[70px] top-1/2 left-[20px] -translate-y-1/2"
          :src="tonImage"
          alt="tons"
        />
        <i18n-t
          class="ml-auto w-fit text-[16px] leading-[22px] text-white text-shadow text-shadow_black whitespace-pre"
          tag="p"
          keypath="reviveWindow.nextTONin"
        >
          <template v-slot:distance>
            <span class="text-[#FFE657]">
              {{ formatNumberWithSeparator(nextTon.height) }}
            </span>
          </template>
        </i18n-t>
      </div>
      <div
        v-if="
          nextEventCoin.height &&
          nextEventCoin.amount &&
          leaguesService.hasAccess(playerLeague, 'customCoinEvent')
        "
        class="relative h-[79px] bg-[#2397D529] w-full py-[18px] pl-[123px] pr-[14px] rounded-[7px] overflow-hidden"
      >
        <img
          class="absolute top-1/2 left-[20px] -translate-y-1/2 w-[60px]"
          :src="eventCoinImage"
          alt="custom coin"
        />
        <i18n-t
          class="text-[16px] leading-[22px] text-white text-shadow text-shadow_black text-right whitespace-pre"
          tag="p"
          keypath="reviveWindow.nextEventCoinIn"
        >
          <template v-slot:distance>
            <span class="text-[#FFE657]">
              {{ formatNumberWithSeparator(nextEventCoin.height) }}
            </span>
          </template>
        </i18n-t>
      </div>
    </div>
    <ProgressBar
      class="h-[32px] w-full mt-[19px]"
      inner-wrapper-class="p-[3px]"
      :progress="reviveInfo?.usedRevivalsAmount ?? 0"
      :goal="reviveInfo?.maxRevivalsAmount ?? 0"
      :is-loading="isGetRevivePending"
    >
      <p class="text-[22px] text-shadow text-shadow_black">
        {{(reviveInfo?.usedRevivalsAmount ?? 0)}}/{{(reviveInfo?.maxRevivalsAmount ?? 0)}}
      </p>
      <template #append>
        <p class="text-[20px] text-shadow text-shadow_black">
          {{ t('reviveWindow.maxRevive') }}
        </p>
      </template>
    </ProgressBar>
    <div class="flex gap-x-3 mt-[7px] mx-auto w-full">
      <VButton
        class="basis-[118px] grow-0 shrink-1"
        type="danger"
        size="large"
        :text="t('reviveWindow.end')"
        :disabled="isPurchasingRevive"
        @click="skipRevive"
      />
      <VButton
        v-if="reviveInfo?.freeIsAvailable"
        class="flex-1"
        type="success"
        size="large"
        :text="t('reviveWindow.freeRevive')"
        :disabled="isPurchasingRevive"
        @click="handleRevive()"
      />
      <VButton
        v-else-if="reviveInfo?.prices"
        class="flex-1"
        type="success"
        size="large"
        :text="reviveInfo.prices[0].price + t('reviveWindow.revive')"
        image-class="horn-bg"
        :disabled="isPurchasingRevive"
        @click="handleReviveHorns"
      />
    </div>
  </ModalWindow>
  <BalanceItem
    class="stars-balance-item"
    iconName="hard-coin-bg"
    balance-class="text-shadow text-shadow_black text-shadow_thin"
  >
    {{ formatNumberToShortString(playerState!.hard ?? 0) }}
  </BalanceItem>
  <PurchaseCurrencyWindow
    ref="purchaseCurrencyWindowRef"
    @purchased="onPurchased"
    @not-enough-funds="purchaseStars"
    @close="startTimer"
  />
</template>

<style lang="scss">
.revive-modal {
  padding: 14px 21px 17px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(360deg, #72c3e6 0%, #b7e9ff 92.65%);
  box-shadow:
    0 2px #00000033,
    inset 0 4px #ffffff;

  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 182px;
    top: 0;
    left: 50%;
    z-index: -1;
    transform: translate(-50%, -50%);
    background: radial-gradient(50% 50% at 50% 50%, #bb7900 0%, rgba(255, 68, 16, 0) 100%);
  }
}

.horn-balance-item {
  position: absolute;
  top: 20px;
  left: 20px;
}

.stars-balance-item {
  position: absolute;
  top: max(var(--inset-top), 16px);
  left: 32px;
  z-index: 10;
  width: fit-content;
}

.event-score-banner {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  width: inherit;
  padding: 6px;

  border: 5px solid #ffd634;
  border-radius: 13px;
  box-shadow: 0 2px #00000033;

  &__title {
    font-size: 18px;
    line-height: 22px;
  }

  &__score {
    font-size: 24px;
    line-height: 32px;
    letter-spacing: 0;
  }

  &::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 20%;
    width: 25%;
    height: 5px;
    background-color: #ffefb0;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: -4px;
    right: 20%;
    width: 25%;
    height: 4px;
    background-color: #ffefb0;
  }
}

.hot-record-highscore {
  background: linear-gradient(360deg, #db302f 0%, #ff7214 92.65%);

  &__image {
    position: absolute;
    width: 105px;
    bottom: -9px;
    left: -20px;

    &:nth-child(2n) {
      left: auto;
      right: -20px;
      transform: scaleX(-1);
    }
  }
}

.onepercent-totalscore {
  background: linear-gradient(360deg, #6309dd 0%, #be67e4 92.65%);

  &__image {
    position: absolute;
    width: 84px;
    bottom: -13px;
    left: -25px;

    &:nth-child(2n) {
      left: auto;
      right: -25px;
      transform: scaleX(-1);
    }
  }
}

@property --deg {
  syntax: '<angle>';
  inherits: false;
  initial-value: 0deg;
}

.clock {
  width: 56px;
  height: 56px;
  background-color: #331400;
  border-radius: 50%;
  padding: 3px;

  &__dial {
    --deg: 0deg;
    --time: 10s;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(transparent var(--deg), #ffa100 0deg, #fa4b01 360deg);
    transition: none;

    &_active {
      transition: --deg var(--time) linear;
      --deg: 360deg;
    }
  }
}
</style>
