<script setup lang="ts">
import { nextTick, ref, watch } from 'vue';
import stars from '@/assets/images/temp/stars.png'
import { useLootboxReward } from '@/stores/rewardStore'
import { useI18n } from 'vue-i18n'
import VOverlay from '../VOverlay.vue'
import { useReward } from '@/composables/useReward'
import type { RewardInfo, RewardType } from '@/services/openapi'

import * as spine from '@esotericsoftware/spine-player'
import '@esotericsoftware/spine-player/dist/spine-player.min.css'

const { t } = useI18n()

const lootBoxStore = useLootboxReward()
const { showRewards } = useReward()

const TRANSITION_TIME = 1000

const spinePlayer = ref<spine.SpinePlayer | null>(null);
const currentAnimation = ref('t5_IDLE');

const isShowingRewards = ref(false)

const nextLootBox = () => {
  isShowingRewards.value = false
  lootBoxStore.nextLootBox()
  setIdleAnimation()
}

const onClick = () => {
  if (!spinePlayer.value || currentAnimation.value === 't5_Rainbow_Appear') return
  setOpenAnimation()
  setTimeout(() => {
    const rewards: RewardInfo[] = lootBoxStore.rewards.map((item) => {
      return {
        multiplier: item.multiplier,
        type: item.type,
        value: item.value
      }
    })
    
    const uniqueRewards = Object.values(
      rewards.reduce((acc: Record<string, RewardInfo>, item) => {
        const { type, value, multiplier } = item;
        if (type === 'skin') {
          acc[value] = { type, value, multiplier };
        } else if (!acc[type] || type === 'fullLives') {
          acc[type] = { type, value, multiplier };
        } else {
          acc[type].value += value;
        }
        return acc;
      }, {} as Record<RewardType, RewardInfo>)
    );
    
    isShowingRewards.value = true
    showRewards(uniqueRewards).then(nextLootBox)
  }, TRANSITION_TIME)
}

watch(() => lootBoxStore.isOpen, (isOpen) => {
  if (isOpen) {
    nextTick(() => {
      new spine.SpinePlayer('player-container', {
        skeleton: '/spine/Revive_Portal.json',
        atlas: '/spine/Revive_Portal.atlas.txt',
        scale: 3,
        preserveDrawingBuffer: true,
        animation: 't5_IDLE',
        animations: ['t5_Rainbow_Appear', 't5_IDLE'],
        showControls: false,
        interactive: false,
        backgroundColor: '#00000000',
        alpha: true,
        showLoading: false,
        success: function (player) {
          spinePlayer.value = player
        },
        error: function (player, reason) {
          alert(reason);
        },
      })
    })
  }
})

const setOpenAnimation = () => {
  if (spinePlayer.value) {
    currentAnimation.value = 't5_Rainbow_Appear'
    spinePlayer.value.setAnimation(currentAnimation.value, false);
  }
}

const setIdleAnimation = () => {
  if (spinePlayer.value) {
    currentAnimation.value = 't5_IDLE'
    spinePlayer.value.setAnimation(currentAnimation.value, true);
  }
}
</script>

<template>
  <VOverlay
    :isOpen="lootBoxStore.isOpen"
    class="overlay_shine overlay_not-transparent reward-screen"
    :class="{ 'opacity-0': isShowingRewards }"
    @click="onClick"
  >
    <div class="text-center tracking-normal space-y-3">
      <p class="text-[32px] leading-[46x] text-shadow">
        {{ t('reward.youGot') }}
      </p>
      <p class="text-[40px] leading-[55x] text-[#FFE657] text-shadow">
        {{ t(`lootboxes.${lootBoxStore.lootboxType}`) }}
      </p>
      <img class="absolute left-0 top-[var(--inset-top)] w-full -z-10" :src="stars" alt="stars" />
    </div>
    <div
      id="player-container"
      class="absolute top-0 left-0 w-full h-full"
      @click="onClick"
    ></div>
    <p
      class="mx-auto text-[24px] font-extrabold text-shadow text-shadow_black text-shadow_thin tracking-normal"
    >
      {{ t('actions.tapToOpen') }}
    </p>
  </VOverlay>
</template>

<style lang="scss">
.lootBox-reward {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .lootbox-rewards-grid__item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 90px;

    &-shine {
      width: 150px;
    }

    &-image {
      width: 60px;
    }
  }

  &__box {
    @keyframes box-hide-animation {
      0% {
        transform: scale(1);
      }
      8% {
        transform: scale(1);
      }
      100% {
        transform: scale(0);
        opacity: 0;
      }
    }

    &_hide {
      animation: box-hide-animation 1s forwards;
    }
  }

  &__explosion {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 50%;
    top: 50%;
    width: 100px;
    transform: translate(-50%, -50%) scale(0);
    z-index: 1000;

    @keyframes explosion-animation {
      0% {
        transform: translate(-50%, -50%) scale(0);
      }
      60% {
        opacity: 1;
      }
      70% {
        transform: translate(-50%, -50%) scale(7);
      }
      100% {
        transform: translate(-50%, -50%) scale(5);
        opacity: 0;
      }
    }

    &_active {
      animation: explosion-animation 2s forwards;
    }
  }
}
</style>
