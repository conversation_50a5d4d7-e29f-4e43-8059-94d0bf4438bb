<script setup lang="ts">
import {
  useRewardStore,
} from '@/stores/rewardStore';
import VOverlay from '@/components/VOverlay.vue';
import MultipleRewardScreen from '@/components/rewards/simple-rewards/MultipleRewardScreen.vue';
import { coinCollectingAnimation } from '@/utils/coinCollectingAnimation'

const store = useRewardStore()

const collect = () => {
  store.rewards.forEach(reward => {
    coinCollectingAnimation(
      [`#${reward.type}-balance-header`, `#${reward.type}-balance-home`, `#${reward.type}-balance-reward`],
      reward.type,
      `#${reward.type}-balance-reward-item`
    )
  })
  store.closeReward()
}
</script>

<template>
  <VOverlay
    :isOpen="store.isOpen"
    class="reward-screen"
    @click="collect"
  >
    <MultipleRewardScreen
      :rewards="store.rewards"
      :image="store.titleImage"
      @collect="collect"
    />
  </VOverlay>
</template>

<style lang="scss">
</style>
