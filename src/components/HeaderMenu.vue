<template>
  <div id="header-bar" class="header-bar">
    <div>
      <slot name="left"></slot>
    </div>
    <div class="ml-auto">
      <slot name="right"></slot>
    </div>
    <slot></slot>
  </div>
</template>

<style lang="scss">
.header-bar {
  position: fixed;
  top: var(--inset-top);
  left: 0;
  z-index: 2;

  display: flex;
  justify-content: space-between;
  align-items: center;

  padding: 0 16px;
  height: var(--header-height-fixed);
  width: inherit;

  background-color: var(--header-color);
}
</style>
