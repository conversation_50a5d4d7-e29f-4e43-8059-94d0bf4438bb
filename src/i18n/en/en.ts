export const en = {
  actions: {
    play: 'Play',
    continue: 'Continue',
    claim: 'Claim',
    getReward: 'Get reward',
    collect: 'Collect',
    tapToPlay: 'Tap\nto play',
    tapToContinue: 'Tap\nto continue',
    tapToJump: 'Tap to Jump',
    tapToShoot: 'Tap to Shoot',
    tapToCollect: 'Tap to collect',
    tapToOpen: 'Tap to open',
    tapToSkip: 'Tap to skip',
    connectWallet: 'Connect Wallet',
    makeTransaction: 'Make Transaction',
    cancelTransaction: 'Cancel Transaction',
    playAgain: 'Play again',
    allowAccess: 'Allow access',
    select: 'Select',
    subscribe: 'Subscribe',
    withdraw: 'Withdraw',
    back: 'Back',
    startFarming: 'Farm',
    leave: 'Leave',
    goToHome: 'Go to Home',
    goToShop: 'Go to shop',
    goToSkins: 'Go to skins!',
    disconnect: 'Disconnect',
    inviteFriend: 'Invite Friend',
    create: 'Create',
    hold: 'Hold for autospin',
    spin: 'Spin',
    stop: 'Stop',
    openChat: 'Open Chat',
    startEvent: 'Start Event',
    join: 'Join',
    got: 'Got it!',
    open_telegram: 'Open Telegram',
    lets_go: "Let's go!",
    check: 'Check'
  },
  search: 'Search',
  comingSoon: 'Coming soon',
  boosters: {
    title: 'Boosters',
    stackableJumper: {
      name: 'Spring',
      fullName: 'Spring',
      description: 'Boosts your coin collection, maximizing your earnings per session.'
    },
    stackableAimbot: {
      name: 'AIM Bot',
      fullName: 'AIM Bot',
      description: 'Boosts your coin collection, maximizing your earnings per session.'
    },
    stackableMagneticField: {
      name: 'Magnet',
      fullName: 'Magnetic Field',
      description: 'Boosts your coin collection, maximizing your earnings per session.'
    },
    endlessRun: 'Endless Run',
    rewards: 'Rewards you can get:',
    select: 'Select boosters:'
  },
  warning: 'Warning',
  mainMenu: 'Main menu',
  pausedOnBack: 'Are you sure you want to leave the game?',
  gameover: 'Game over',
  totalScore: 'Total Score',
  currentScore: 'Current score:',
  allowGyroscope: 'Allow\nthe Gyroscope',
  free: 'Free',
  selected: 'Selected',
  hotRecord: 'Hot Record',
  joinUs: 'Join us!',
  joinCommunity: 'Join community',
  termsOfUse: 'Terms of Use',
  faq: 'FAQ',
  swipeScreen: 'Swipe the\nscreen to move',
  event_ends_at: 'Event ends at: {time}',
  event_ended: 'Event ended',
  total_prize_pool: 'Total prize pool',
  features: {
    farming: 'Farming',
    dailyReward: 'Daily Rewards',
    onePercentEvent: '1% Event',
    hotRecordEvent: 'Hot Record',
    tonMiningEvent: 'Ton Mining',
    withdraw: 'Withdraw',
    customCoinEvent: 'Skin Event',
    dynamicCoins_1: 'To the Moon',
    dynamicCoins_2: 'Deep Dive',
    lives: 'Lives',
    clanEvent: 'Clan Event',
    battleEvent: 'Battle Event',
    puzzleCoins: 'Ice Fragment'
  },
  jackpot: {
    title: 'Jackpot',
    results: 'Results',
    results_description: 'Previous Jackpot Winners',
    rewards: 'Rewards',
    rewards_distribution: 'Distribution of awards by place',
    player: '{amount} Player | {amount} Players',
    coupons: '+{amount} Jackpot coupon | +{amount} Jackpot coupons',
    coupons_description: 'Collect as many coupons as possible to win'
  },
  me: {
    skins: 'Skins',
    collections: 'Collections'
  },
  skins: {
    newSkin: 'You got new skin!',
    filter: {
      all: 'All',
      owned: 'Owned',
      not_owned: 'Not owned'
    },
    collections: {
      0: 'Funny',
      1: 'Telegram',
      2: 'Nature',
      3: 'Animal',
      4: 'Am not Food',
      5: 'Mem',
      6: 'Hero',
      7: 'Sly',
      8: 'Lucky'
    },
    list: {
      '-1': 'Uni',
      0: 'Dizzy Uni',
      1: 'Emo-Uni',
      2: 'Uni Plumber',
      3: 'Uni Kitty',
      4: 'Uni Ninja',
      5: 'McUni',
      6: 'Uni Potter',
      7: 'Devil Uni',
      8: 'Super Uni',
      9: 'Resistance Uni',
      10: 'Uni Diver',
      11: 'Chick Uni',
      12: 'Capy Uni',
      13: 'Uni Poop',
      14: 'Pepe Uni',
      1000: 'Uni Inu',
      1001: 'Uni Trump',
      1002: 'Chainsaw Uni',
      1003: 'SWAG Uni',
      1004: 'Uni Fremen',
      1005: 'Grump Uni',
      1006: 'Peck Uni',
      1007: 'Uni Duo',
      1008: 'Spring Uni',
      1009: 'Uni Magic',
      1010: 'Uni Luck',
      2000: 'Chill Uni',
      2001: 'Mystic Uni',
      2002: 'Uni Beat',
      2003: 'Uni Relic',
      2004: 'Uni Reaper',
      2005: 'Uni Inferno',
      2006: 'Uni Bera',
      2007: 'Ponke Uni',
      2008: 'Uni Bull',
      2009: 'Uni Doge',
      2010: 'Uni Popcat',
      2011: 'Uni Pengu',
      2012: 'Uni Ronald',
      2013: 'Uni Art',
      2014: 'Anon Uni',
      2015: 'Uni Shroom',
      2016: 'Uni Princess',
      2017: 'Uni Cloud',
      2018: 'Uni Fairy',
      2019: 'Uni Mixture',
      2020: 'Uni Genie',
      2021: 'Uni Rubik',
      2022: 'Uni Gift',
      2023: 'Uni Rich',
      2024: 'Uni Neko',
      2025: 'Uni Rabbit',
      2026: 'Uni Dice',
      2027: 'Uni Radio',
      2028: 'Uni NightSky',
      2029: 'Uni Lumpy',
      2030: 'Uni Rocket',
      2031: 'Uni Luna',
      2032: 'Astro Uni',
      2033: 'Uni Basket',
      2034: 'Chicken Uni',
      2035: 'Uni Bunny',
      2036: 'Uni Carrot',
      2037: 'Uni Egg',
      2038: 'Uni Tulip',
      2039: 'Uni Citizen',
      2040: 'Uni TNT',
      2041: 'Uni Zombie',
      2042: 'Uni Diamond',
      2043: 'Uni Steve',
      2044: 'Uni Ender',
      2045: 'Uni Creeper',
      2046: 'Uni Agnes',
      2047: 'Uni Edith',
      2048: 'Uni Balthazar',
      2049: 'Uni Kevin',
      2050: 'Uni Evil',
      2051: 'Uni Gru',
      2052: 'Uni Vector',
      2053: 'Uni Waider',
      2054: 'Jedi Uni',
      2055: 'Sith Uni',
      2056: 'Uni Storm',
      2057: 'Grogu Uni',
      2058: 'Wookiee Uni',
      2059: 'Mando Uni',
      2060: 'Captain Uni',
      2061: 'Hulk Uni',
      2062: 'Iron Uni',
      2063: 'Uni Loki',
      2064: 'Spider Uni',
      2065: 'Thanos Uni',
      2066: 'Thor Uni',
      2067: 'Uni Po',
      2068: 'Shifu Uni',
      2069: 'Mr. Ping Uni',
      2070: 'Kai Uni',
      2071: 'Tai Lung Uni',
      2072: 'Tigress Uni',
      2073: 'Oogway Uni',
      2074: 'Gary Uni',
      2075: 'Krabs Uni',
      2076: 'Patrick Uni',
      2077: 'Sponge Uni',
      2078: 'Plankton Uni',
      2079: 'Sandy Uni',
      2080: 'Squidward Uni',
      2081: 'Shrek Uni',
      2082: 'Dragon Uni',
      2083: 'Fiona Uni',
      2084: 'Gingy Uni',
      2085: 'Pinocchio Uni',
      2086: 'Donkey Uni',
      2087: 'Red Cat Uni',
      2088: 'Robin Uni',
      2089: 'Uni Green',
      2090: 'Uni Aquaman',
      2091: 'Uni Batman',
      2092: 'Uni Cyborg',
      2093: 'Uni Flash',
      2094: 'Uni Wonder',
      2095: 'Uni Fred',
      2096: 'Uni Velma',
      2097: 'Uni Daphne',
      2098: 'Uni Doo',
      2099: 'Uni Jade',
      2100: 'Uni Sub-Zero',
      2101: 'Uni Kitana',
      2102: 'Uni Scorpion',
      2103: 'Uni Cat',
      2104: 'Uni Dog',
      2105: 'Uni Toybear',
      2106: "Uni in Durov's Cap",
      2107: 'Plush Uni',
      2108: 'Uni Scared Cat',
      2109: 'Uni Shitjumper',
      2110: 'Uni Boinker',
      2111: 'Uni Marty',
      2112: 'Uni Gloria',
      2113: 'Uni Melman',
      2114: 'Uni Alex',
      2115: 'Uni TON',
      2116: 'Uni Solana',
      2117: 'Uni Hustle',
      2118: 'Hustle Bera',
      2119: 'Hustle Brett',
      2120: 'Hustle Pepe',
      2121: 'Uni Tonjumper',
      2122: 'Uni Mr. SOON',
      2123: 'Uni Snow',
      2124: 'Uni Daenerys',
      2125: 'Uni Drogon',
      2126: 'Uni Night King',
      2127: 'Uni Ice',
      2128: 'Uni Lava',
      2129: 'Uni Dino',
      2130: 'Uni Yeti',
      2131: 'Uni Koalin',
      2132: 'Uni Alia',
      2133: 'Uni Matchce',
      2134: 'Uni Money',
      2135: 'Fit Panda',
      2136: 'Panda in Cap',
      2137: 'Panda Mario',
      2138: 'Lucky Panda',
      2139: 'Uni Nicejump',
      2140: 'Uni Nicegram',
      2141: 'Uni Girl',
      2142: 'Uni Burger',
      2143: 'Uni Cheeze',
      2144: 'Uni Eggcorn',
      2145: 'Uni Uran',
      2146: 'Uni Fool',
      2147: 'Uni Cacao',
      2148: 'Uni Panda',
      2149: 'Uni Pear',
      2150: 'Uni Scramble',
      2151: 'Uni Sheep',
      2152: 'Uni Stalker',
      2153: 'Uni Paper',
      2154: 'Uni Fight Me',
      2155: 'Uni Fight Elf',
      2156: 'Uni Fight Hero',
      2157: 'Uni Fight Bear',
      2158: 'Adventure Uni',
      2159: 'Uni Labrador',
      2160: 'Ducky Uni',
      2161: 'Ducky Doctor',
      2162: 'Ducky Crocodile',
      2163: 'Ducky King',
      2164: 'Uni Samurai',
      2165: 'Rich Dog',
      2166: 'Rich Uni',
      3000: 'Bored Uni'
    },
    requirenments: {
      inviteFriend: 'Invite more friends: {amount}',
      wallet: 'Connect wallet',
      transaction: 'Make first transaction',
      box: 'GET SKINS!',
      dailyReward: 'Days to earn: {days}',
      inProgress: 'In progress...'
    }
  },
  achievements: {
    title: 'Achievements',
    description: 'Get a bonus for your multiplier!',
    completed: 'Completed',
    tapToClaim: 'Tap to claim',
    list: {
      1: {
        1: {
          name: 'Happy Together',
          description: 'Invite 1 referral'
        },
        2: {
          name: 'Local Boss',
          description: 'Invite 10 referrals'
        },
        3: {
          name: 'Referral Flexer',
          description: 'Invite 100 referrals'
        },
        4: {
          name: 'Influencer',
          description: 'Invite 500 referrals'
        },
        5: {
          name: 'Leader of Unicorns',
          description: 'Invite 1000 referrals'
        }
      },
      2: {
        1: {
          name: 'Oops...',
          description: 'Revive 1 time'
        },
        2: {
          name: 'More than a cat',
          description: 'Revive 10 times'
        },
        3: {
          name: "I'll be back",
          description: 'Revive 100 times'
        },
        4: {
          name: 'Not today',
          description: 'Revive 1 000 times'
        },
        5: {
          name: 'Fenix',
          description: 'Revive 10 000 times'
        }
      },
      3: {
        1: {
          name: 'New addiсtion',
          description: 'High score: \n10 000'
        },
        2: {
          name: 'Jump Student',
          description: 'High score: \n100 000'
        },
        3: {
          name: 'Up! Up! Up!',
          description: 'High score: \n300 000'
        },
        4: {
          name: 'Jump Master',
          description: 'High score: \n500 000'
        },
        5: {
          name: 'To The Moon',
          description: 'High score: \n1 000 000'
        }
      },
      4: {
        1: {
          name: 'Poor Student',
          description: 'Total soft currency spent: \n10 000'
        },
        2: {
          name: 'Smart Spender',
          description: 'Total soft currency spent: \n100 000'
        },
        3: {
          name: 'Scrooge McDuck',
          description: 'Total soft currency spent: \n500 000'
        },
        4: {
          name: 'Gold Millionaire',
          description: 'Total soft currency spent: \n1 000 000'
        },
        5: {
          name: 'Golden Card',
          description: 'Total soft currency spent: \n2 000 000'
        }
      },
      5: {
        1: {
          name: 'Boing Boing',
          description: 'Jump on 10 monsters'
        },
        2: {
          name: 'Oops, Squished',
          description: 'Jump on 30 monsters'
        },
        3: {
          name: 'Jumpocalypse',
          description: 'Jump on 50 monsters'
        },
        4: {
          name: 'Bouncy Revenge',
          description: 'Jump on 100 monsters'
        },
        5: {
          name: 'Smashed Snacks',
          description: 'Jump on 300 monsters'
        }
      },
      6: {
        1: {
          name: 'Ouch Maker',
          description: 'Shoot 10 monsters '
        },
        2: {
          name: 'Shotgun Surgeon',
          description: 'Shoot 30 monsters '
        },
        3: {
          name: 'Needlework',
          description: 'Shoot 50 monsters '
        },
        4: {
          name: 'Hit and Run',
          description: 'Shoot 100 monsters '
        },
        5: {
          name: 'Hit Parade',
          description: 'Shoot 300 monsters '
        }
      },
      7: {
        1: {
          name: 'Boosted to the Moon',
          description: 'Use propeller/jetpack 3 times in a game'
        },
        2: {
          name: 'Too Much Juice',
          description: 'Use propeller/jetpack 5 times in a game'
        },
        3: {
          name: 'Boosted Beyond Belief',
          description: 'Use propeller/jetpack 10 times in a game'
        }
      },
      8: {
        1: {
          name: 'First Blood',
          description: 'Kill 100 monsters'
        },
        2: {
          name: 'Angry Killer',
          description: 'Kill 500 monsters'
        },
        3: {
          name: 'Punisher',
          description: 'Kill 1 000 monsters'
        },
        4: {
          name: 'Terminator',
          description: 'Kill 5 000 monsters'
        },
        5: {
          name: 'Beast Mode On',
          description: 'Kill 10 000 monsters'
        }
      },
      9: {
        1: {
          name: 'Platform Smasher',
          description: 'Break 10 fragile platforms in a game'
        },
        2: {
          name: 'Fattie',
          description: 'Break 50 fragile platforms in a game'
        },
        3: {
          name: 'Grounded too soon',
          description: 'Break 100 fragile platforms in a game'
        },
        4: {
          name: 'I broke it again',
          description: 'Break 300 fragile platforms in a game'
        },
        5: {
          name: 'Falling Star',
          description: 'Break 500 fragile platforms in a game'
        }
      },
      10: {
        1: {
          name: 'Engine warm-up',
          description: 'Fly with jetpack 10 times'
        },
        2: {
          name: 'Ace Pilot',
          description: 'Fly with jetpack 50 times'
        },
        3: {
          name: 'Lift Off',
          description: 'Fly with jetpack 200 times'
        },
        4: {
          name: 'Jetpack Junkie',
          description: 'Fly with jetpack 1 000 times'
        },
        5: {
          name: 'Altitude Addict',
          description: 'Fly with jetpack 2 500 times'
        }
      },
      11: {
        1: {
          name: 'Fan-tastic game',
          description: 'Fly with propeller 10 times'
        },
        2: {
          name: 'Drone Pilot',
          description: 'Fly with propeller 50 times'
        },
        3: {
          name: 'Wind Power Wizard',
          description: 'Fly with propeller 200 times'
        },
        4: {
          name: 'Airstream King',
          description: 'Fly with propeller 1 000 times'
        },
        5: {
          name: 'Propeller Pro 2500',
          description: 'Fly with propeller 2 500 times'
        }
      },
      12: {
        1: {
          name: 'New Wardrobe',
          description: 'Unlock 1 skin'
        },
        2: {
          name: 'Say yes to the skin',
          description: 'Unlock 5 skin'
        },
        3: {
          name: 'Swag',
          description: 'Unlock 10 skin'
        },
        4: {
          name: 'Elite Style Collector',
          description: 'Unlock 20 skin'
        },
        5: {
          name: 'God of Style',
          description: 'Unlock 40 skin'
        }
      },
      13: {
        1: {
          name: 'Score Noob',
          description: 'Total score across all games: \n10 000'
        },
        2: {
          name: "Collect 'Em All",
          description: 'Total score across all games: \n100 000'
        },
        3: {
          name: 'Sleep is Overrated',
          description: 'Total score across all games: \n1 000 000'
        },
        4: {
          name: 'Grind Mode Activated',
          description: 'Total score across all games: \n10 000 000'
        },
        5: {
          name: 'Forbes 100 under 100',
          description: 'Total score across all games: \n100 000 000'
        }
      },
      14: {
        1: {
          name: "Hoppin' Around",
          description: 'Make 1 000 jumps'
        },
        2: {
          name: 'Bounce Champ',
          description: 'Make 10 000 jumps'
        },
        3: {
          name: 'Skywalker in Training',
          description: 'Make 100 000 jumps'
        },
        4: {
          name: 'First class jumper',
          description: 'Make 250 000 jumps'
        },
        5: {
          name: "Sky's the Limit",
          description: 'Make 1 000 000 jumps'
        }
      },
      15: {
        1: {
          name: 'Bounce Rookie',
          description: 'Jump 10 times on a trampoline'
        },
        2: {
          name: 'Up, Down, Try Again',
          description: 'Jump 100 times on a trampoline'
        },
        3: {
          name: 'Springing Into Trouble',
          description: 'Jump 1 000 times on a trampoline'
        },
        4: {
          name: 'Knees Still Bouncing',
          description: 'Jump 10 000 times on a trampoline'
        },
        5: {
          name: 'Trampoline Master',
          description: 'Jump 100 000 times on a trampoline'
        }
      },
      16: {
        1: {
          name: 'Spring Along Rookie',
          description: 'Jump 10 times on a spring'
        },
        2: {
          name: 'Bounce Nausea',
          description: 'Jump 100 times on a spring'
        },
        3: {
          name: 'Knees are done',
          description: 'Jump 1 000 times on a spring'
        },
        4: {
          name: "I can't do it anymore",
          description: 'Jump 10 000 times on a spring'
        },
        5: {
          name: 'Who Needs Trampolines?',
          description: 'Jump 100 000 times on a spring'
        }
      }
    }
  },
  lootboxes: {
    rainbowLootBox: 'Rainbow Box',
    luckyLootBox: 'Lucky Box',
    magicLootBox: 'Magic Box',
    fightLootBox: 'Fight Box',
    duckyLootBox: 'Ducky Box'
  },
  nextFreeAfter: 'Next free after:',
  soon: 'Soon',
  portraitOrientationRequired: 'Use portrait orientation for better experience',
  loading: 'Loading',
  settings: {
    title: 'Settings',
    music: 'Music',
    sound: 'Sound',
    haptic: 'Vibration',
    language: 'Language',
    support: 'Support'
  },
  farming: {
    start: 'Start Farming',
    farming: 'Farming'
  },
  friends: {
    title: 'Friends',
    inviteFriends: 'Invite Friends',
    description: 'Get a bonus for each friend invited!',
    invitedCount: 'Your referrals: {count}',
    freeInviteDesc: 'Per friend',
    premiumInviteDesc: 'Per friend with Premium',
    emptyRefs: 'Invite friends to get juicy\nrewards and have fun together'
  },
  earn: {
    title: 'Earn',
    name: 'Tasks',
    description: 'Complete tasks and earn rewards',
    timeToVerify: 'Task verification time 1h',
    startTasks: 'Start Tasks',
    dailyTasks: 'Daily Tasks',
    completedMissions: 'Completed Missions',
    tasksCompleted: 'Tasks completed:',
    missions: {
      title: 'Missions',
      partners: 'Partners',
      invite_5_friend: {
        name: 'Invite 5 friends',
        action: 'Invite'
      },
      invite_3_friend: {
        name: 'Invite 3 friends',
        action: 'Invite'
      },
      invite_1_friend: {
        name: 'Invite 1 friend',
        action: 'Invite'
      },
      connect_wallet: {
        name: 'Connect TON wallet',
        action: 'Connect'
      },
      first_transaction: {
        name: 'Make first TON transaction',
        action: 'Make transaction'
      },
      subscribe_main_channel: {
        name: 'Subscribe to the main channel',
        action: 'Subscribe'
      },
      use_booster: {
        name: 'Catch {goal} boosters',
        action: 'Play'
      },
      jump_to_score: {
        name: 'Score {goal} in one game',
        action: 'Play'
      },
      kill_monster: {
        name: 'Kill {goal} monsters',
        action: 'Play'
      },
      invite_ref: {
        name: 'Invite {goal} friend(s)',
        action: 'Invite'
      },
      play_game: {
        name: 'Play {goal} game(s)',
        action: 'Play'
      },
      catch_ticket: {
        name: 'Catch {goal} Ticket',
        action: 'Play'
      },
      daily_total_jump: {
        name: 'Jump {goal} point in total',
        action: 'Play'
      },
      use_aimbot_booster: {
        name: 'Use {goal} AIM Bot',
        action: 'Play'
      },
      use_jumper_booster: {
        name: 'Use {goal} Spring',
        action: 'Play'
      },
      use_magnet_booster: {
        name: 'Use {goal} Magnet',
        action: 'Play'
      },
      unlock_league: {
        name: 'Unlock {goal} League',
        action: 'Play'
      },
      buy_skin: {
        name: 'Buy a skin',
        action: 'Go to skins'
      },
      use_revive: {
        name: 'Use {goal} revive(s)',
        action: 'Play'
      },
      subscribe_x: {
        name: 'Follow our X',
        action: 'Follow'
      },
      subscribe_community_chat: {
        name: 'Join Uni Jump community group',
        action: 'Join'
      },
      add_to_home_screen: {
        name: 'Add Uni Jump to the home screen',
        action: 'Add to home screen'
      },
      purchase_in_shop_for_stars: {
        name: 'Spend 100 Telegram Stars in the Store',
        action: 'Purchase'
      },
      purchase_skin_for_stars: {
        name: 'Purchase any Skin for Stars',
        action: 'Purchase'
      },
      boost_telegram_channel: {
        name: 'Boost our Telegram channel',
        action: 'Boost'
      },
      go_to_miniapp_9: {
        name: 'Play Match-3, watch ads, and earn USDt!',
        action: 'Play MatchMoney'
      },
      go_to_miniapp_10: {
        name: 'Boinker: spin the slot and collect Artifacts',
        action: 'Join Boinkers'
      },
      go_to_miniapp_15: {
        name: 'Play Miner to win $300',
        action: 'Play Miner'
      },
      go_to_miniapp_19: {
        name: 'Open chests — get USDT',
        action: 'Join Digger game'
      },
      go_to_miniapp_22: {
        name: 'Start merging cars today!',
        action: 'Join DRFT Party'
      },
      go_to_miniapp_23: {
        name: 'You Play - We Really Pay!',
        action: 'Join BrickWalls'
      },
      go_to_miniapp_24: {
        name: 'Play Outmine',
        action: 'Join Outmine'
      },
      go_to_miniapp_25: {
        name: 'Breed ducks to earn $EGG tokens!',
        action: 'Play DuckyGram'
      },
      go_to_miniapp_26: {
        name: 'Launch Symptomify',
        action: 'Play Symptomify'
      },
      go_to_miniapp_27: {
        name: 'Play WORK DOGS',
        action: 'Join WORK DOGS'
      },
      go_to_miniapp_28: {
        name: 'Tap & Earn $HASH',
        action: 'Play HashCats'
      },
      go_to_miniapp_29: {
        name: 'Check in, open boxes & get $RICH',
        action: 'Join RichDogs'
      },
      go_to_miniapp_30: {
        name: 'Play Simple Tap',
        action: 'Join Simple Tap'
      },
      go_to_miniapp_31: {
        name: 'Play Pookie & get TON boxes!',
        action: 'Play Pookie Cheese'
      },
      go_to_miniapp_32: {
        name: 'Mine TON in TonTower',
        action: 'Play TonTower'
      },
      go_to_miniapp_33: {
        name: 'Play Fasqon & eran FSQN tokens',
        action: 'Join Fasqon'
      },
      go_to_miniapp_34: {
        name: 'Play Capybara MEME',
        action: 'Join Capybara MEME'
      },
      go_to_miniapp_35: {
        name: 'Launch 🚀 and withdraw $$$',
        action: 'Join Space Adventure'
      },
      go_to_miniapp_36: {
        name: 'Get Free Stars🌟',
        action: 'Join StarCoin'
      },
      go_to_miniapp_37: {
        name: 'Join Daily Combo Updates',
        action: 'Join Daily Combo'
      },
      go_to_miniapp_38: {
        name: 'Play JustFab',
        action: 'Join JustFab'
      },
      go_to_miniapp_39: {
        name: 'Join TAPX',
        action: 'Join TAPX'
      },
      go_to_miniapp_40: {
        name: 'Play Pixiland & Claim $wPIXI now!',
        action: 'Join Pixiland'
      },
      go_to_miniapp_41: {
        name: 'Play Biz Tycoon Now',
        action: 'Join Biz Tycoon  '
      },
      go_to_miniapp_42: {
        name: 'Join Appss',
        action: 'Join Appss'
      },
      go_to_miniapp_43: {
        name: 'Join Puparty and spin 1 slot',
        action: 'Join Puparty'
      },
      go_to_miniapp_44: {
        name: 'Join Gumart',
        action: 'Join Gumart'
      },
      go_to_miniapp_45: {
        name: 'Join Pokergram & Grab your free chips now!',
        action: 'Join Pokergram'
      },
      go_to_miniapp_46: {
        name: 'Play Agent301',
        action: 'Play Agent301'
      },
      go_to_miniapp_47: {
        name: 'Get Telegram gift at Empty',
        action: 'Join Empty'
      },
      go_to_miniapp_48: {
        name: 'Earn USDT in GenkiMiner!',
        action: 'Join GenkiMiner'
      },
      go_to_miniapp_49: {
        name: 'Play Beetz in early access',
        action: 'Join Beetz'
      },
      go_to_miniapp_50: {
        name: 'Win with Pepecase',
        action: 'Join Pepecase'
      },
      go_to_miniapp_51: {
        name: 'Join RewardsHQ and win $$$',
        action: 'Join Rewards HQ'
      },
      partners_task_1: {
        name: 'Spin wheel 10 times',
        action: 'Join Boinkers'
      },
      partners_task_2: {
        name: 'Sent 3 Boinkers to the Moon',
        action: 'Join Boinkers'
      },
      partners_task_3: {
        name: 'Join Pokergram',
        action: 'Join Pokergram'
      },
      partners_task_4: {
        name: 'Complete tutorial in MemHustle',
        action: 'Join MemHustle'
      },
      partners_task_5: {
        name: 'Purchase MemHustle Fort',
        action: 'Join MemHustle'
      },
      subscribe_mem_hustle_channel: {
        name: 'Join MemHustle channel',
        action: 'Join MemHustle'
      },
      partners_task_6: {
        name: 'Complete TON Station Daily Combo',
        action: 'Join TON Station'
      },
      partners_task_7: {
        name: 'Spin Lucky Wheel 5 times',
        action: 'Join TON Station'
      },
      subscribe_ton_station_channel: {
        name: 'Subscribe to TON Station TG Channel',
        action: 'Join TON Station'
      },
      partners_task_sleepagotchi_1: {
        name: 'Play Sleepagotchi LITE',
        action: 'Join Sleepagotchi'
      },
      partners_task_sleepagotchi_2: {
        name: 'Download Sleepagotchi App',
        action: 'Join Sleepagotchi'
      },
      partners_task_match_money_1: {
        name: 'Complete 3 levels in the game (3 spins)',
        action: 'Join Match Money'
      },
      partners_task_match_money_2: {
        name: 'Complete 10 levels in the game (10 spins)',
        action: 'Join Match Money'
      },
      partners_task_match_money_3: {
        name: 'Subscribe to the channel (3 magnet)',
        action: 'Join Match Money'
      },
      partners_task_panda_1: {
        name: 'Join PandaFit game',
        action: 'Join PandaFit'
      },
      partners_task_panda_2: {
        name: 'Join PandaFit channel',
        action: 'Join PandaFit'
      },
      partners_task_nicegram_1: {
        name: 'Download Nicegram App',
        action: 'Join Nicegram'
      },
      partners_task_nicegram_2: {
        name: 'Join Nicegram Channel',
        action: 'Join Nicegram'
      },
      partners_task_nicegram_3: {
        name: ' Join Nicegram X',
        action: 'Join Nicegram'
      },
      partners_task_fight_me_1: {
        name: 'Play 3 battles in Fight Me',
        action: 'Join Fight Me'
      },
      partners_task_fight_me_2: {
        name: 'Join Fight Me channel',
        action: 'Join Fight Me'
      },
      partners_task_labrador_1: {
        name: 'Play Labrador Adventures',
        action: 'Join Labrador!'
      },
      partners_task_labrador_2: {
        name: 'Finish 1 Expedition',
        action: 'Join Labrador!'
      },
      partners_task_labrador_3: {
        name: 'Finish 1 digging cycle',
        action: 'Join Labrador!'
      },
      partners_task_duckygram_1: {
        name: 'Hatch duck',
        action: 'Play Duckygram'
      },
      partners_task_duckygram_2: {
        name: 'Breed duck',
        action: 'Play Duckygram'
      },
      partners_task_duckygram_3: {
        name: 'Earn 1 EGG in Duckygram',
        action: 'Play Duckygram'
      }
    },
    error: {
      partnerTaskNotCompleted: 'Partner task is not completed'
    }
  },
  shop: {
    title: 'Shop',
    friendsDescription: 'Buy a virtual friend and get \ninstant bonuses like a real one!',
    bestDeal: 'Best Deal',
    new: 'NEW',
    free: 'FREE',
    skinBoxDescription: 'Skin drop',
    other: 'Other',
    nextFree: 'Next free after'
  },
  airdrop: {
    button: 'Drop',
    title: 'Airdrop',
    tasks: {
      info: 'Complete tasks to participate in the Airdrop!',
      connectWallet: 'Connect your TON wallet',
      transaction: 'Make TON transaction'
    },
    ticketsBanner: 'Tickets is the only way to get Airdrop, more tickets — more rewards',
    instructionSteps: {
      1: '1. Connect your TON wallet',
      2: "2. Let's make your first transaction to prove you are ready for Airdrop! It costs only 0.5 TON."
    },
    instructionText:
      'You can get TON for your wallet from any of the following exchanges:\n      Binance, Bybit, KuCoin, OKX or Bitget.',
    comingSoon: 'AIRDROP IS COMING SOON!'
  },
  wallet: {
    title: 'Wallet',
    assets: 'Assets',
    connectWallet: 'Сonnect your wallet to withdraw',
    selectAsset: 'Select an asset to withdraw',
    disconnectWalletAlert:
      'Are you sure you want to disconnect your wallet?\n\nWithout a connected wallet, you cannot request a withdrawal!',
    history: 'History',
    details: {
      title: 'Details',
      amount: 'Amount',
      fee: 'Fee',
      address: 'Address',
      status: 'Status',
      lastUpdate: 'Last Update'
    },
    emptyHistory: 'No history yet',
    withdraw: {
      title: 'Withdraw',
      yourWallet: 'Your wallet',
      amount: 'Amount',
      available: 'Available {amount}',
      minimum: 'Minimum {amount} {currency}',
      successMessage:
        'Withdrawal has been\nsuccessfully sent.\n\nIt can take up to 3 days to\nprocess the transaction.'
    },
    missions: {
      multiplier: 'Collect {target} Multiplier',
      ton_amount: 'Collect {target} TON'
    }
  },
  blockers: {
    default: 'Reach {target}\nto {feature}',
    league: 'Reach {league}\nto unlock {feature}',
    goal: '{target} goal | {target} goals'
  },
  leagues: {
    title: 'Leagues',
    league: '{league} League',
    description: 'Rating is based on the tickets balance'
  },
  playerProfile: {
    title: 'Profile',
    allTimeScore: 'All Time High Score',
    averageScore: 'Average Score',
    gamesPlayed: 'Games Played'
  },
  reward: {
    title: 'Reward',
    youGot: 'You got',
    tickets: 'Tickets',
    soft: 'Coins',
    hard: 'Stars',
    magicHorns: 'Horns',
    refs: 'Friends',
    refsFake: 'Friends',
    boxes: 'Boxes',
    stackableMagneticField: 'Magnet',
    stackableJumper: 'Spring',
    stackableAimbot: 'AIM Bot',
    timeBoundMagneticField: 'Magnet',
    timeBoundJumper: 'Spring',
    timeBoundAimbot: 'AIM Bot',
    unlimitedLives: 'Unlimited lives',
    fullLives: 'Full lives',
    ton: 'TON',
    lives: 'Lives',
    customCoin: 'Eggs Coin',
    battleCoin: 'Rich Coin',
    dynamicCoins_1: 'Moon Coin',
    dynamicCoins_2: 'Shell Coin',
    puzzleCoins: 'Fragment',
    rewards: 'Rewards',
    wheelSpins: 'Wheel Spins | Spins',
    boosters: 'Boosters'
  },
  magnetFields: {
    magnet: 'MAGNET',
    magnetic_field_1: 'SMALL',
    magnetic_field_2: 'LARGE'
  },
  dailyRewards: {
    title: 'Daily Rewards',
    info: 'Come back tomorrow for the new rewards!',
    skinInfo: ' Login daily and get {skin} Skin with {bonus} ticket bonus',
    day: 'Day',
    almostThere: 'Almost there',
    youNeedRefs: 'You need more friends to unlock'
  },
  subscription: {
    description: "You're doing well!",
    details: 'To continue collect TON \n— join our channel'
  },
  achievementRewards: {
    newAchievement: 'New Achievement'
  },
  onepercent: {
    description: 'You need to have more than {targetScore} total score to get rewarded',
    eventStartDescription: 'You need to have more than\n{targetScore} total score to get',
    eventBlockedDescription:
      'Invite at least 1 friend to join the event. Or buy a virtual one in our shop. Prize pool:',
    eventEndDescription:
      'The event is over!\nYou scored {targetScore} points in total.\nYour reward:'
  },
  hotrecord: {
    description: 'You need to have more high score to get reward',
    eventEndDescription: 'The event is over!\nYour record is {highScore}.\nYour reward:',
    letsGo: "Let's go!"
  },
  customCoin: {
    description: 'Buy {box} and win skins to farm {x} more coins! Compete for {ton} and {stars}',
    eventStartDescription: 'You need to have more coins to get reward',
    boost: 'Up to {x} Coin boost'
  },
  battle_event: {
    team_select: 'Choose your team:',
    team_score: 'Team Score',
    boost: {
      title: 'Boost',
      description: 'Boost. Earn. Win TON.',
      instruction: 'Collect up to 175x more Rich coins with boost!'
    },
    reward: {
      winner: 'Your community has won! Claim reward:',
      loser: 'Your community has lost. Claim reward:'
    },
    missions: {
      jumping: 'Boost Jumping skill',
      luck: 'Boost Luck',
      power: 'Boost Power',
      magic: 'Boost Magic',
      partners: 'Complete all community Tasks'
    }
  },
  tonEvent: {
    ton: 'TON',
    nextPoolIn: 'Next pool in',
    eventLimitDescription: 'DAILY POOL: ',
    letsPlay: "Let's play!",
    eventStartDescription: 'Take your chance and try to catch TON!',
    eventDescription:
      'The daily pool: {limit}.\nTotal event pool: {totalLimit}.\nIncrease the pool by inviting friends.\nEvery {friends} friends +{bonus}!',
    eventNoTonAvailableDescription: 'Oops... The daily TON limit is reached.\n Try again tomorrow!'
  },
  clan_event: {
    description: 'Collect more tickets to win more TON'
  },
  tonLimit: {
    description: "You've collected {ton}\n To collect more TON unlock \n {league}"
  },
  skinForTon: {
    description:
      'Buy exclusive skin for {ton} Just sent a transaction and claim unique Bored Uni skin.'
  },
  reviveBanner: {
    description: 'Once you fall you can use Stars to continue jumping and collect more TON!'
  },
  deepDiveOffer: {
    description: 'Complete to collect the grand prize'
  },
  fragmentOffer: {
    description: 'Unlock the entire picture to get the grand prize'
  },
  lootBoxOffer: {
    description:
      'Open 5 Boxes with limited Ducky Skins and win prizes from 30 TON and 20 000 Stars Pool.'
  },
  endsIn: 'Ends in ',
  exploiters: {
    ton: 'TON',
    heist: 'Heist',
    task: 'Kill {count} in 1 game',
    count: 'Kill {count} Exploiters',
    lastChance: 'LAST CHANCE!',
    collectDescription: 'Nice job! Your TON is safe now. Collect it!',
    lastChanceDescription: 'Oops... You have the last chance to get TON back! Try now.',
    welcomeDescription: 'Warning! Exploiters have stolen your TON. Get it back!'
  },
  controlMethod: {
    gyroscope: 'Gyroscope',
    swipe: 'Swipe',
    selectMsg: 'Select your preferable type of control',
    selected: 'Selected',
    select: 'Select'
  },
  reviveWindow: {
    title: 'Continue?',
    score: 'Score: {score}',
    highScore: 'High Score: {score}',
    newHighscore: 'New High Score {score}!',
    pointsLeftToHighscore: 'Only {score} points to beat\nyour High Score!',
    nextTONin: 'Only {distance} points to\ncatch TON!',
    nextEventCoinIn: 'Only {distance} points to\ncatch Eggs!',
    saveMe: 'SAVE ME!',
    maxRevive: 'Max Revive',
    freeRevive: 'Free Revive',
    revive: ' Revive',
    end: 'End'
  },
  lives: {
    full: 'Full',
    moreLives: 'More Lives',
    noMoreLives: 'No More Lives',
    timeToNext: 'Time to next life',
    inviteFriendToGet: ' Invite friend to get full lives!',
    inviteFriend: 'Invite Friend',
    goToShop: 'Go to Shop'
  },
  clans: {
    title: 'Clan',
    clans: 'Clans',
    myClan: 'My Clan',
    topClans: 'Top Clans',
    event: {
      name: 'Clan Wars',
      total_prize: 'Total prize is\n{ton}',
      description_1: 'Participate in Clan Events\nand win up to {ton}',
      requirenment: 'Invite more friends\nto start the event'
    }
  },
  state: {
    yourScore: 'Your score',
    ticketsCollected: 'Tickets collected',
    coinsCollected: 'Coins collected'
  },
  connection: {
    title: 'Connection lost',
    description: 'Please check your Internet connection to continue playing'
  },
  longLoad: {
    title: 'Important!',
    shortDescription: 'Loading is taking longer than expected.',
    fullDescription:
      'Please make sure you have a good network connection and kindly wait until all assets are loaded.'
  },
  errors: {
    appVersion: 'Please update Telegram to the latest version',
    walletNotConnectedForTransaction: 'You need to connect your wallet before making a transaction'
  },
  warnings: {
    inviteToCollect: 'Invite more friends to collect'
  },
  contest: {
    successCaption: 'Congratulations! You are participating in the giveaway!',
    failCaption:
      'You have not completed all tasks. Please read the giveaway conditions and try again!',
    errorCaption: 'Giveaway is not available.',
    task: {
      tickets: 'Collect {value} tickets',
      friends: 'Invite {value} friends',
      multiplier: 'Reach multiplier of {value}',
      skin: 'Unlock "{value}" skin',
      starsTotal: 'Spent {value} stars in total',
      starsDuringContest: 'Spent {value} stars'
    }
  },
  instructions: {
    hot_record: {
      1: 'Play Uni Jump',
      2: 'Reach<br/><span class="instruction__step-text_yellow">MAX</span> Record',
      3: 'Earn Stars',
      4: '<span class="instruction__step-text_blue">1000</span> best players<br/>will get <span class="instruction__step-text_yellow">Stars</span>'
    },
    one_percent: {
      1: 'Play Uni Jump',
      2: 'Reach <span class="instruction__step-text_yellow">Best</span> total<br/>score',
      3: 'Earn Stars'
    },
    ton_mining: {
      1: 'Play Uni Jump',
      2: 'Collect TON',
      3: 'Use MAGNET for<br/>collect <span class="instruction__step-text_yellow">X3</span> TON'
    },
    custom_coin: {
      1: 'Grab exclusive skins<br/>from limited-time boxes',
      2: 'Play and collect coins - each skin unlocks a better coin multiplier!',
      3: 'More coins — higher rank, better rewards!'
    },
    leagues: {
      1: 'Upgrade<br/><span class="instruction__step-text_yellow">X</span> Multiplier',
      2: 'Collect more<br/>tickets',
      3: 'Unlock new <span class="instruction__step-text_yellow">Leagues</span>',
      4: 'New <span class="instruction__step-text_yellow">Leagues</span> unlock<br/>more Events!'
    },
    ice_fragment: {
      1: 'Play Uni Jump',
      2: 'Collect Coin',
      3: 'Get Rewards!'
    },
    clan_create: {
      1: 'Create<br/>Telegram Group',
      2: 'Add <span class="instruction__step-text_yellow">UniJump</span> bot',
      3: 'Give bot admin rights,<br/>as Admin',
      4: 'Invite friends to the group<br/>and grow the clan!'
    },
    clan_event: {
      1: 'Invite friends<br/>to the Clan',
      2: 'Reach <span class="instruction__step-text_yellow">500</span> members<br/>of 3 League',
      3: 'Start<br/>the Event',
      4: 'Get <span class="instruction__step-text_blue">50 TON</span> Reward!'
    },
    battle_event: {
      1: 'Choose your team',
      2: 'Play Uni Jump',
      3: 'Collect unique coin',
      4: 'Try to win and get<br/>more rewards'
    }
  },
  hoursFull: 'Hours',
  days: 'd',
  hours: 'h',
  minutes: 'm',
  minutesFull: 'Minutes',
  seconds: 's',
  linkCopied: 'Link copied',
  error: 'Error',
  claimed: 'Claimed',
  canceled: 'Canceled',
  success: 'Success',
  pending: 'Pending',
  processing: 'Processing'
}
